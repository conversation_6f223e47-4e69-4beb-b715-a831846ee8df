---
title: 量化好声音播客
description: 量化好声音播客 - 探讨量化交易的各种话题
---

# 量化好声音播客

欢迎收听"量化好声音"播客！在这里，我们探讨量化交易的各种话题，从因子研究到策略实现，从市场洞察到技术分享。

## 最新播客

<div class="podcast-grid">
  <div class="podcast-card">
    <div class="podcast-info">
      <h3><a href="/podcast/02-ICU均线.md">02-ICU均线</a></h3>
      <p class="podcast-date">2025-07-09</p>
      <p class="podcast-desc">02-ICU均线</p>
      <audio controls><source src="https://cdn.jsdelivr.net/gh/zillionare/podcast@main/2025/07/02-final.MP3" type="audio/mpeg">您的浏览器不支持音频播放。</audio>
    </div>
  </div>
  <div class="podcast-card">
    <div class="podcast-info">
      <h3><a href="/podcast/01-A股动量因子如何实现.md">01-A股动量因子如何实现</a></h3>
      <p class="podcast-date">2025-07-09</p>
      <p class="podcast-desc">01-A股动量因子如何实现</p>
      <audio controls><source src="https://cdn.jsdelivr.net/gh/zillionare/podcast@main/2025/07/01-final.MP3" type="audio/mpeg">您的浏览器不支持音频播放。</audio>
    </div>
  </div>
</div>

<style>
.podcast-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.podcast-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: #fff;
}

.podcast-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.podcast-info {
  padding: 20px;
}

.podcast-info h3 {
  margin: 0 0 10px 0;
  font-size: 1.2em;
}

.podcast-info h3 a {
  text-decoration: none;
  color: #333;
}

.podcast-info h3 a:hover {
  color: #007acc;
}

.podcast-date {
  color: #666;
  font-size: 0.9em;
  margin: 5px 0;
}

.podcast-desc {
  font-size: 0.95em;
  color: #555;
  margin: 10px 0;
  line-height: 1.4;
}

.podcast-info audio {
  width: 100%;
  margin-top: 15px;
}
</style>
