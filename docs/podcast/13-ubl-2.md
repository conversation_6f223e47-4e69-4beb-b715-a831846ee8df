---
title: 13-ubl-2
description: 13-ubl-2
date: 2025-07-09
audio: https://cdn.jsdelivr.net/gh/zillionare/podcast@main/2025/07/13-final.MP3
---
!!! note "Aaron"
    量化好声音 睡前听一听。大家好！我是Aaron

!!! tip "Flora"
    我是Flora，欢迎回到量化好声音。

!!! note "Aaron"
    今天我们要讨论的是『匡醍译研报』的一期文章。这篇文章讲述了我们是如何复现东吴证券高子剑等人的研报，《上下影线因子，蜡烛好还是威廉好》。文章一共分两期。今天要讲的是第二期。

!!! tip "Flora"
    好的。这篇研报应该说是非常有价值。它提出的UBL因子，根据我们的复现，不仅在研报中指出的2009年到2020年非常有效，就是在2020年之后，我们发现它仍然有效。

!!! note "Aaron"
    是这样的。我们做这期研报，其实还有另外一重原因，就是我们的学员曾经给我们提过一个问题：上完《因子分析与机器学习策略》课程之后，基础知识是打牢了，但遇到研报和论文，还是不知道怎么复现，问我们能不能给些帮助。

!!! tip "Flora"
    对，实际上就是缺乏练习的机会。那我们选中这篇研报，是因为它在工程化上比较简单，并且研报作者给了很清晰明了的步骤图，所以，非常适合我们练手。

!!! note "Aaron"
    所以最近我们好几期节目，有播客的也有视频的，都是围绕学员的这个问题来提供方法。

Flora：对，所以学员如果有什么要求，在学完课程之后，也是可以跟我们提出的。

!!! note "Aaron"
    那好，我们就切入到今天的正题。Flora，你能不能先给大家介绍下，这份研报中的因子，是基于一个什么原理？

!!! tip "Flora"
    这份研报啊，其实是基于一句股谚，头上三柱香，不死也赔光。说得就是如果股价在高位出现三根长上影线，那么接下来大概率要跌。

!!! note "Aaron"
    对，股谚是基于大家交易经验的长期总结。但是它有几成概率正确，现在是不是还有效，这些就需要我们以量化模型的角度来加以考察了。

!!! tip "Flora"
    正是。上一期我们复现了UBL因子的构建，这一期就要回答大家最关心的问题，UBL因子倒底有多能打？

!!! note "Aaron"
    没错，这就是今天要重点讨论的。不过，事情还有一点曲折。

!!! tip "Flora"
    怎么说？

!!! note "Aaron"
    在挖掘因子的时候，最难的部分当然是如何把一个想法，转换成量化模型。这一点，上一期我们已经完成了。但在进行因子检验时，一般我们都要用第三方的框架来进行回测以及结果可视化。

!!! tip "Flora"
    对，只有通过第三方的检验，才能保证结果是客观公正的，是可以在不同的场合下复现的。所以，你是在进行因子检验时，遇到了第三方不配合的情况了吗？

!!! note "Aaron"
    正是如此。一般情况下，我们会使用Alphalens来进行因子检验。这也是一个事实上的标准了。但是，这一次，当我们使用Alphalens时，结果直接报错了。错误信息是，我们翻译一下，大致是，推导出的频率与传入的频率不一致。

!!! tip "Flora"
    这个错误看上去莫测高深啊。所以这是一种什么情况呢？

!!! note "Aaron"
    问题出在**数据频率不匹配**上。UBL因子是一个月度因子，只在每个月末才有数据，并且是在次月初以开盘价买入，次月末以收盘价卖出。但Alphalens期待的是连续的日期索引，这样才能计算远期收益，所以无法处理我们的因子数据。

!!! tip "Flora"
    那Alphalens有没有推荐的解决方案？

!!! note "Aaron"
    有，但不够优雅。它建议按日计算因子，然后设置periods参数为[21, 105, 210]来模拟月度收益。21就相当于一个月嘛，他们是这么想的。但这种方法有个问题——不是每个月都刚好21个交易日，所以在处理跨月问题上，并不准确。

!!! tip "Flora"
    看来需要自己开发工具了。你是怎么解决的？

!!! note "Aaron"
    我开发了一个叫**Moonshot**的简单回测库，专门处理月度因子。核心思路很直接：对每个在月末有因子数据的资产，在次月初以开盘价买入，月末以收盘价卖出，计算收益。

!!! tip "Flora"
    这个思路很清晰。你的问题是解决了，那如果听众朋友也遇到类似的问题，那可以用这个库吗？

!!! note "Aaron"
    我把这个简单的回测库做成了一个python包，在github上开源了。所以，如果你遇到类似的问题，就可以直接使用moonshot这个框架了。

!!! tip "Flora"
    Moonshot。你总是爱起一些奇奇怪怪的名字。我记得你之前还开发过一个把markdown格式渲染成网页，可合各个平台发布的工具，叫什么marktwain，这名字也是不走寻常路。现在说回到UBL因子回测，所以，根据研报，这个因子非常不错，你复现的结果能确认这个结论吗？

!!! note "Aaron"
    确实复现出来了！不止是UBL因子，就连UBL基于的两个原始因子，结果都相当不错。比如上影线标准差因子，这个因子实际上是个反向因子，是很好的"见顶指标"。从分组收益看，第一组（因子值最小）收益最高，第五组（因子值最大）收益最低，呈现明显的单调性和相关性。

!!! tip "Flora"
    我看过这篇文章。实际上，你复现的结论，要比研报还要好一些。这是为什么？

!!! note "Aaron"
    有三个原因。第一，我们没有计算手续费；第二，无法精准复现研报的股票池；第三，研报中很多技术细节被省略了，所以完全复现很困难，但是，仍然可以确认，研报的结论是正确的，到今天为止，因子仍然有效。

!!! tip "Flora"
    那另一个基础因子，威廉下影线因子的表现如何？

!!! note "Aaron"
    也跟研报有一致的结论。不过，这里有个**反直觉的发现**。威廉下影线均值因子显示，下影线均值越小，后市上涨概率越高。这与我们的直觉经验似乎相反。

!!! tip "Flora"
    是这样吗？这似乎跟研报开头提到的示例也不太一致，所以研报有进行解释吗？

!!! note "Aaron"
    很可惜研报没有进行解释。我的理解是，从量化角度看，威廉下影线均值因子值较低时，表明过去一段时间股票经常以接近最低价收盘。这种情况往往出现在**超跌反弹**的前夜。当股票持续承压后，是不是就蕴含了**均值回归**的机会。

!!! tip "Flora"
    嗯~~ 也就是说，尽管量化模型的结论与直觉不符，但，仔细一想，量化模型的结论也是能站得住脚的。还有一个问题，按理说，人类的经验也是一种基于大数据的学习过程。为什么这次的情况刚好相反呢？

!!! note "Aaron"
    这就跟记忆的形成原理有关了。我们的主观记忆只会留下少数印象深刻的时刻，却"遗忘"了大量平凡的日子。但统计上看，正是那些平凡的日子，在复利作用下，才是我们人生的路标。主观记忆这种选择性，正是主观经验与量化分析的分野。

!!! tip "Flora"
    这个解释很有道理。从研报和你复现的结果看，其实蜡烛上影线标准差因子和威廉下影线均值因子的表现就已经够好了，那为什么研报还要将这两个因子进行线性等权叠加？

!!! note "Aaron"
    这是为了构建出一个更稳健的因子。这样构建出来的UBL因子，尽管在分层累计收益与单个因子差不多，但它的风险调整收益要高出不少，也就是收益没有下降太多，但是回撤下降不少。

!!! tip "Flora"
    这在投资中是非常理想的状态。我看了你的文章，有一句话我很喜欢：在投资中，比起鳞鳞远峰见，我们更喜欢淡淡平湖春。我们热爱这些45度仰望星空的净值曲线。

!!! note "Aaron"
    对，在人生路上，我算是风险偏好者，但在投资上，我是风险厌恶者。所以这也解释了为什么我最终选择了做量化交易。

!!! tip "Flora"
    其实多数选择量化交易的人，都是风险厌恶者。梭哈这种事，在量化中是不存在的。我还有一个问题，研报只回测到2020年4月，那这个因子，后来的表现如何？

!!! note "Aaron"
    这个留给读者自己去验证。在我们的Quantide Research Platform上可以调整参数自己跑，应该会有惊喜。

!!! tip "Flora"
    最后再聊文章最后提到的一个技术细节。在计算UBL因子时，为什么要进行截面zscore处理？

!!! note "Aaron"
    这是个很好的问题。按研报要求，需要对上影线标准差因子和威廉下影线均值因子按日进行截面zscore处理。但我认为这可能不是必要的。

首先，zscore有**nan传染性**问题。也就是说，如果某天有一支股票的因子值是nan，那么计算截面上的zscore时，会导致该日所有股票的zscore都是nan。所以我们在计算zscore时，不能使用默认参数 ，而必须选择`nan_policy='omit'`这个参数。
    其次，zscore化不会改变同一日因子间的排序，而分组收益计算正是按排序进行的。所以截面zscore化可能只是习惯，对分组累计收益计算没有实质影响。

!!! tip "Flora"
    这次讨论让我对因子检验有了更深的理解。从Alphalens的局限性，到自主开发Moonshot工具，再到对研报结果的深入分析，整个过程体现了量化研究的严谨性。

!!! note "Aaron"
    是的，这个案例很好地展示了**从理论到实践的完整链条**。不仅要能构建因子，更要能有效检验。工具的选择和开发同样重要。最关键的是，要保持对结果的理性分析，大胆质疑，不被表面现象迷惑。

!!! tip "Flora"
    那么对于想要进入量化领域的朋友，你有什么建议？

!!! note "Aaron"
    三点建议。第一，**掌握扎实的编程基础**，能够灵活处理各种数据问题；第二，**培养批判性思维**，不盲从现有工具和方法；第三，**注重工程化实现**，理论再好，无法落地就没有价值。

!!! tip "Flora"
    当然每晚收听量化好声音也非常重要。量化投资的魅力就在于，它让我们能够用数据和逻辑去验证直觉，发现那些隐藏在市场噪音中的真正规律。这也让我想起了一位学员跟我们分享的，量化吸引他的原因，就是如果自己的模型能和预期能够fit上，这本身就是一件令人热血沸腾的事情。

!!! note "Aaron"
    确实如此。好，这期的研报解读就是这样。Flora，听说你已经在UBL因子的视频了对吧？

!!! tip "Flora"
    对，新一期的宽粉读研报视频很快就会在小红书、B站等平台跟大家见面啦。所以对想学习量化的朋友，你可以通过公众号读到我们的文章，在播客和视频中，我们会带大家深入分析相关的技术要点和细节。最后，如果你需要这些因子的代码，可以加入我们的Quantide Research Platform，成为会员，就可自己动手运行一下了。

!!! note "Aaron"
    对。我知道大家在看一些网上的文章，包括研报和论文时，常常会遇到代码不全、没有数据等等问题，常常是花了时间，但就连作者的观点是否正确都不知道。所以，我们才为大家准备了这样一个平台，这样我们的所有文章，都可以通过运行来检验它的结论。

!!! tip "Flora"
    对。这个会员现在一年的价格才360元，相当于每天不到1元钱，相当超值，也是我们给早鸟会员的专享价。今天的节目就到这里，我们下期再见。

!!! note "Aaron"
    下期见！