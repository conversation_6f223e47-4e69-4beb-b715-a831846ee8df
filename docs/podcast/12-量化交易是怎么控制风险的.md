---
title: 12-量化交易是怎么控制风险的
description: 12-量化交易是怎么控制风险的
date: 2025-07-08
audio: https://cdn.jsdelivr.net/gh/zillionare/podcast@main/2025/07/12-final.mp3
---

<style>
.bg-light {
    background-color: #fcfefe;
    padding: 10px 15px;
    margin-bottom: 5px;
    border-radius: 5px;
}
.bg-dark {
    background-color: #f8f9fa;
    padding: 10px 15px;
    margin-bottom: 5px;
    border-radius: 5px;
}
</style>

<div class="bg-light"><p><strong>Flora</strong>: 量化好声音，睡觉听一听，大家好，我是Flora</p></div>
<div class="bg-dark"><p><strong>Aaron</strong>: 欢迎大家回到量化好声音，我是Aaron</p></div>
<div class="bg-light"><p><strong>Flora</strong>: 今天我们要聊的话题是，量化交易是怎么控制风险的。 在主观投资中，我们是凭借推理、直觉和经验来冲锋陷阵，准确度很差。到底有多差呢？在水晶球那期节目中，我们介绍了这样一个实验，就是维克托.哈加尼在2024年做的一个实验。维克托.哈加尼也是个名人，他曾是著名的长期资本管理公司的创始合伙人之一，现在是ELM财富的CEO。</p></div>
<div class="bg-dark"><p><strong>Aaron</strong>: 在这个实验中，哈加尼找了100多个接受过金融训练的年轻人，来玩一个游戏：交易标普500的指数。他们可以看到前一天的华尔街日报，他们要根据这个信息来进行交易。时间是从08年到2022年之间随机的选。</p></div>
<div class="bg-light"><p><strong>Flora</strong>: 这相当于是从未来穿越了。所以这项实验被称为水晶球实验。因为水晶球有预言未来的意思。那这个实验的灵感来自于塔勒布，就是著名的畅销书《黑天鹅》系列的作者，它在2016年的一条推文中写到，如果你提前 24 小时告诉投资者第二天的消息，他将在不到一年的时间内破产。</p></div>
<div class="bg-dark"><p><strong><!--这个游戏可以在这里玩： https</strong>: //elmwealth.com/crystal-ball-challenge/ --></p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 结果呢，水晶球实验真的支持了塔尼布的观点。这些人当中有一半人赔了钱，六分之一的人直接就爆仓了。你想这些人都是有知识有信息的，但是在这种主观的判断下面，他们依然没有办法很好的去控制风险。量化呢就是通过历史数据的回测，以及一些科学的方法，可以让你的预测更精准，所以它在风险上面会更小</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 对，首先是，量化交易更能做出科学的决策。当你有一个idea时，做主观投资的人，可能当时一拍脑袋就冲进去了，结果被套时，才发现自己的假设不对。而量化交易则不同，你可以先拿历史数据，对你的想法进行检验。这个过程，量化人把它叫做回测。如果自己的想法在历史上都从来没有赚过钱，那么你就洗洗睡吧，不用做了。</p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 但是这里有一个很大的但是，对量化人来说，也常常听说一句话，叫做回测买地球，实盘亏成狗。听上去，如果回测很不错，反倒更可能亏钱，这么看，不是加大了风险了吗？</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 这实际上并不是量化本身的问题，而是做量化的人的基本素养、或者说流程的问题。刚好前几天也有一位学员提问，在写完了一个多因子模型策略之后，有没有比较好的、系统的方法来验证它是否正确。我记得你当时在群里回答他，大概是提了三个建议，对吧？</p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 对。这位同学是做出来了模型，而且回测结果不错。因为如果回测结果不好，大概率自己会觉得哪里写错了，会不停地调优的。但是现在出了个好的结果，反倒不敢信了。</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 他这种意识很好。</p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 对，那我给他的建议是三点。首先，每个策略都会有多个变量，比如，回测时间，universe（也就是股票池），还有一些是策略独有的参数。我们可以修改这些参数，看看与之对应的，策略收益的变化情况。比如，在我们修改回测的时间和股票之后，如果回测结果发生较大变化，那我们要确保这些变化能够得到解释。</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 不是说这种情况下,回测结果不应该变化，而是说，要确保这些变化能够得到解释。</p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 对。这两个变量一般来说，对策略的影响都很大，所以，发生变化是可以理解的。但我们要能得到一致性的解释。那如果保持回测时间和universe这两个变量不变，对一些参数进行微调时，如果结果有较大变化，那就说明回测模型当中，是有过拟合存在的，这个模型就不能用。</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 这个说法背后的原理是所谓的参数高原。也就是模型如果是鲁棒的，那么，多数情况下，参数的微小变化，不会引起模型性能的显著变化。这样就会在参数邻近的位置，出现收益比较接近的一片区域。</p></div>
<div class="bg-light"><p>Aaron： 对，参数高原原理是我们检查模型是否存在过拟合的一个重要方法。第二点是，有一些错误是系统性的，比如，引用的数据，特别是财务数据，可能存在未来数据；你使用的回测框架又不能帮你排除错误，而你自己也没有意识到这种错误。这种情况就会出现系统性错误。也就是，不管参数怎么调，数据都很好。举个例子如果你的回测模型常常出现涨停板买入的情况，那么回测结果就会很好。</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 我记得有一年有一个段子，就是如何在一年内，从一万变成一个亿。然后他们真的给出了这样一条路径。就是一月份买入某个股票，吃到十几个涨停板后，然后换成股票B，又吃几个涨停板，就这样接力下去，最终用不到一年，就可以从一万变成一个亿。</p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 正是如此，所以，如果你的股票池很大，回测时间又长，你甚至很难发觉，你的策略收益其实是由这样一些不正常的交易撑起来的。</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 所以，需要换一些更严谨的回测框架，能自动限制涨停板买入、跌停板卖出的。此外呢，框架还有要自动限制T0交易，正确处理复权等等功能。</p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 对，这些方法需要一定的技巧和投入。这里我再介绍一个最简单的方法，就是策略仿真。仿真就是让策略接受实盘数据，做出买卖决定，但只做纸面交易。运行一段时间，如果仿真阶段的表现与回测差异不大，那么我们就可以比较放心地付诸实盘了。</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 对。在仿真阶段，我们需要每天去评估像夏普、最大回撤，胜率，alpha，beta，等等指标。那什么时候可以结束仿真？</p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 最重要是在仿真期能积累足够多的数据，比如交易次数、交易标的数。这样相关的数据才可以进行比较。在交易次数足够多的情况下，如果数据还能跟回测保持接近，那么就可以比较放心地进行实盘了。</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 说到实盘，其实在这一阶段，量化可以控制风险的手段就更多了。听说你在实盘风控上有过惨痛的经历吧？</p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 往事不堪回首啊。发生过两次乌龙指。就是本来要决定卖的个股，结果在操作时，把全仓卖出，点成了全仓买入。最要命的是，我判断还很准，所以，这样遭受的损失就可想而知了。</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 亏都能亏出凡尔赛哈。所以你从此就转量化了。</p></div>
<div class="bg-light"><p><strong>Aaron</strong>: 啊，是的。因为量化是程序化交易，所以，一旦程序跑通，就不会发生这样的错误。还有就是说，你心里想着我要止损，但真到那个时候，你又下不去手了。那量化它就可以帮你严格的执行这个纪律。</p></div>
<div class="bg-dark"><p><strong>Flora</strong>: 还有，做主观交易，理论上讲我们需要每天复盘。但是每天的数据计算都会很花时间，就可能没法及时得到结果。但是量化交易的话，你可以每天都看到你的很多的策略指标，那你就可以很及时的去发现说，我的这个策略现在表现是不是有异常，那我可以很快的去调整</p></div>
<div class="bg-light"><p>Aaron： 所以说，做量化更容易发现风险，更好地控制风险。</p></div>
<div class="bg-dark"><p><!--5. 量化交易确实有优势，普通人能转吗，成本会不会很高？--></p></div>
<div class="bg-light"><p><strong>Flora</strong>: 听起来量化确实是个宝。那你觉得个人如果想要去尝试量化交易的话，会不会因为机构投资者的存在，而完全没有胜算呢？</p></div>
<div class="bg-dark"><p><strong>Aaron</strong>: 其实并不会。因为量化这个赛道，它其实并不是一个资金容量特别大的赛道，就机构其实也挺受限的，因为它的钱太多了，它要进来出去的话，市场冲击成本太大了，然后它的这个回测和实盘的差异会非常的大。那这个反而是我们个人投资者，小资金的一个优势，就是我们可以更精准的去回测我们的这个交易，也不会对市场造成什么冲击，所以我们可以以我们想要的价格成交</p></div>
<div class="bg-light"><p><strong>Flora</strong>: 我们在昨天的节目中，介绍过简街的困局，其实它就是因为量化赛道的资金容量并不大，才不得不冒违规的风险，通过操纵市场来赚钱。</p></div>
<div class="bg-dark"><p><strong>Aaron</strong>: 对，大资金可以为市场定价（也就是预测），却无法以自己的定价成交；而小资金尽管无法为市场定价，却可以预测的价格成交。所以对大资金来说，他们是没有办法去做精准的回测的。没有回测，就没有量化。</p></div>
<div class="bg-light"><p><strong>Flora</strong>: 对，No root, no fruit。回测就是量化的根。今天我们聊了这么多关于量化，他是如何通过科学的决策、严谨的回测、以及严格的执行，来帮我们更好地控制风险，以及我们个人在这个领域独特的优势。</p></div>
<div class="bg-dark"><p><strong>Aaron</strong>: 今天的节目就到这里。匡醒量化好声音每晚播出，欢迎订阅。</p></div>
<div class="bg-light"><p><strong>Flora</strong>: 投资就是一个江湖，也欢迎加入我们在各个媒体上的圈子和社群，与大家一起共同学量化。我们明晚再见。</p></div>
<div class="bg-dark"><p><strong>Aaron</strong>: 再见！</p></div>
