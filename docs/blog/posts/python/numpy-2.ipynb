import numpy as np
import time
import concurrent.futures
import os

# --- 蒙特卡洛布莱克-斯科尔斯期权定价函数 ---
def black_scholes_monte_carlo(S0, K, T, r, sigma, num_simulations, num_steps):
    """
    使用蒙特卡洛模拟计算欧式看涨期权价格。
    S0: 初始股票价格
    K: 行权价格
    T: 到期时间（年）
    r: 无风险利率
    sigma: 波动率
    num_simulations: 模拟路径数量
    num_steps: 每条路径的步数
    """
    dt = T / num_steps
    # 生成股票价格路径的随机部分
    # NumPy 的这些操作（如随机数生成、指数、累积乘积等）通常在底层 C 实现中会释放 GIL。
    # 但在自由线程 Python 中，Python 解释器本身的 GIL 限制被移除，
    # 使得 Python 层的线程管理也能实现真正的并行。
    z = np.random.standard_normal((num_simulations, num_steps))
    daily_returns = np.exp((r - 0.5 * sigma**2) * dt + sigma * np.sqrt(dt) * z)
    price_paths = S0 * np.cumprod(daily_returns, axis=1)

    # 计算期末股票价格
    ST = price_paths[:, -1]

    # 计算看涨期权收益
    payoffs = np.maximum(ST - K, 0)

    # 折现到现值
    option_price = np.exp(-r * T) * np.mean(payoffs)
    return option_price

# --- 性能对比方法 ---

def run_single_threaded(S0, K, T, r, sigma, num_simulations, num_steps):
    start_time = time.perf_counter()
    price = black_scholes_monte_carlo(S0, K, T, r, sigma, num_simulations, num_steps)
    end_time = time.perf_counter()
    return price, end_time - start_time

def run_multi_threaded(S0, K, T, r, sigma, total_simulations, num_steps, num_threads):
    simulations_per_thread = total_simulations // num_threads
    results =
    start_time = time.perf_counter()

    # 使用 ThreadPoolExecutor 进行多线程并行
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures =
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())

    end_time = time.perf_counter()
    # 对所有线程的结果取平均
    final_price = np.mean(results)
    return final_price, end_time - start_time

def run_multi_processed(S0, K, T, r, sigma, total_simulations, num_steps, num_processes):
    simulations_per_process = total_simulations // num_processes
    results =
    start_time = time.perf_counter()

    # 使用 ProcessPoolExecutor 进行多进程并行
    with concurrent.futures.ProcessPoolExecutor(max_workers=num_processes) as executor:
        futures =
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())

    end_time = time.perf_counter()
    # 对所有进程的结果取平均
    final_price = np.mean(results)
    return final_price, end_time - start_time

# --- 主程序执行 ---
if __name__ == "__main__":
    # 期权参数
    S0 = 100      # 初始股票价格
    K = 105       # 行权价格
    T = 1.0       # 到期时间（年）
    r = 0.05      # 无风险利率
    sigma = 0.2   # 波动率

    # 模拟参数
    total_simulations = 10_000_000 # 蒙特卡洛路径总数
    num_steps = 252               # 每条路径的步数（例如，一年中的交易日）
    num_cores = os.cpu_count()    # 获取 CPU 核心数

    print(f"--- 蒙特卡洛期权定价性能对比 ---")
    print(f"期权参数: S0={S0}, K={K}, T={T}, r={r}, sigma={sigma}")
    print(f"模拟参数: 总模拟次数={total_simulations}, 每条路径步数={num_steps}")
    print(f"检测到的 CPU 核心数: {num_cores}\n")

    # 1. 单线程执行
    print("运行单线程蒙特卡洛模拟...")
    price_single, time_single = run_single_threaded(S0, K, T, r, sigma, total_simulations, num_steps)
    print(f"单线程价格: {price_single:.4f}, 耗时: {time_single:.4f} 秒\n")

    # 2. 多线程执行（在 GIL 启用/传统 Python 环境中）
    # 注意：对于 CPU 密集型任务，在 GIL 启用的 Python 中，多线程通常不会带来显著加速，
    # 甚至可能因为线程管理开销而变慢。这是因为 GIL 限制了 Python 字节码的并行执行。
    # 只有当底层 C 扩展（如 NumPy 的大部分操作）释放 GIL 时，才能看到部分并行。
    # 多线程的真正优势体现在自由线程 Python 环境中。
    if num_cores > 1:
        print(f"运行多线程蒙特卡洛模拟（{num_cores} 线程）在 GIL 启用 Python 中（预期行为）...")
        price_multi_thread_gil, time_multi_thread_gil = run_multi_threaded(S0, K, T, r, sigma, total_simulations, num_steps, num_cores)
        print(f"多线程（GIL 启用）价格: {price_multi_thread_gil:.4f}, 耗时: {time_multi_thread_gil:.4f} 秒")
        print(f"相对于单线程加速比: {time_single / time_multi_thread_gil:.2f}x\n")

        # 3. 多进程执行（传统上绕过 GIL 的方法）
        print(f"运行多进程蒙特卡洛模拟（{num_cores} 进程）...")
        price_multi_process, time_multi_process = run_multi_processed(S0, K, T, r, sigma, total_simulations, num_steps, num_cores)
        print(f"多进程价格: {price_multi_process:.4f}, 耗时: {time_multi_process:.4f} 秒")
        print(f"相对于单线程加速比: {time_single / time_multi_process:.2f}x\n")

    print("--- 要观察 NumPy 2.3.0 和自由线程 Python 的全部优势 ---")
    print("您需要使用一个支持自由线程的 Python 解释器来运行此脚本（例如，Python 3.13t 或更高版本，通过无 GIL 构建）。")
    print("在这种环境中运行，'多线程' 执行预计将显示显著加速，可能与 '多进程' 执行相当或超越，因为开销更低。")
    print("\n例如，如果安装了自由线程 Python 并命名为 'python3.13t'，则运行命令为：")
    print("python3.13t your_script_name.py")