{"cells": [{"cell_type": "code", "execution_count": null, "id": "1d646e11", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (*********.py, line 47)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 47\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mresults =\u001b[39m\n             ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m invalid syntax\n"]}], "source": ["import concurrent.futures\n", "import os\n", "import time\n", "\n", "import numpy as np\n", "\n", "\n", "# 蒙特卡洛布莱克-斯科尔斯期权定价函数\n", "# 此代码为AI生成，仅供比较性能用\n", "def black_scholes_monte_carlo(S0, K, T, r, sigma, num_simulations, num_steps):\n", "    \"\"\"\n", "    使用蒙特卡洛模拟计算欧式看涨期权价格。\n", "    S0: 初始股票价格\n", "    K: 行权价格\n", "    T: 到期时间（年）\n", "    r: 无风险利率\n", "    sigma: 波动率\n", "    num_simulations: 模拟路径数量\n", "    num_steps: 每条路径的步数\n", "    \"\"\"\n", "    dt = T / num_steps\n", "    # 生成股票价格路径的随机部分\n", "    z = np.random.standard_normal((num_simulations, num_steps))\n", "    daily_returns = np.exp((r - 0.5 * sigma**2) * dt + sigma * np.sqrt(dt) * z)\n", "    price_paths = S0 * np.cumprod(daily_returns, axis=1)\n", "\n", "    # 计算期末股票价格\n", "    ST = price_paths[:, -1]\n", "\n", "    # 计算看涨期权收益\n", "    payoffs = np.maximum(ST - K, 0)\n", "\n", "    # 折现到现值\n", "    option_price = np.exp(-r * T) * np.mean(payoffs)\n", "    return option_price\n", "\n", "\n", "\n", "def run_single_threaded(S0, K, T, r, sigma, num_simulations, num_steps):\n", "    start_time = time.perf_counter()\n", "    price = black_scholes_monte_carlo(S0, K, T, r, sigma, num_simulations, num_steps)\n", "    end_time = time.perf_counter()\n", "    return price, end_time - start_time\n", "\n", "def run_multi_threaded(S0, K, T, r, sigma, total_simulations, num_steps, num_threads):\n", "    simulations_per_thread = total_simulations // num_threads\n", "    results = []\n", "    start_time = time.perf_counter()\n", "\n", "    # 使用 ThreadPoolExecutor 进行多线程并行\n", "    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:\n", "        futures = [executor.submit(black_scholes_monte_carlo, S0, K, T, r, sigma, simulations_per_thread, num_steps) for _ in range(num_threads)]\n", "        for future in concurrent.futures.as_completed(futures):\n", "            results.append(future.result())\n", "\n", "    end_time = time.perf_counter()\n", "    # 对所有线程的结果取平均\n", "    final_price = np.mean(results)\n", "    return final_price, end_time - start_time\n", "\n", "def run_multi_processed(S0, K, T, r, sigma, total_simulations, num_steps, num_processes):\n", "    simulations_per_process = total_simulations // num_processes\n", "    results = []\n", "    start_time = time.perf_counter()\n", "\n", "    # 使用 ProcessPoolExecutor 进行多进程并行\n", "    with concurrent.futures.ProcessPoolExecutor(max_workers=num_processes) as executor:\n", "        futures = [executor.submit(black_scholes_monte_carlo, S0, K, T, r, sigma, simulations_per_process, num_steps) for _ in range(num_processes)]\n", "        for future in concurrent.futures.as_completed(futures):\n", "            results.append(future.result())\n", "\n", "    end_time = time.perf_counter()\n", "    # 对所有进程的结果取平均\n", "    final_price = np.mean(results)\n", "    return final_price, end_time - start_time\n", "\n", "# --- 主程序执行 ---\n", "if __name__ == \"__main__\":\n", "    # 期权参数\n", "    S0 = 100      # 初始股票价格\n", "    K = 105       # 行权价格\n", "    T = 1.0       # 到期时间（年）\n", "    r = 0.05      # 无风险利率\n", "    sigma = 0.2   # 波动率\n", "\n", "    # 模拟参数 - 先用较小的数值测试\n", "    total_simulations = 5_000_000  # 蒙特卡洛路径总数（减少用于测试）\n", "    num_steps = 252               # 每条路径的步数（例如，一年中的交易日）\n", "    num_cores = os.cpu_count()    # 获取 CPU 核心数\n", "\n", "    print(f\"--- 蒙特卡洛期权定价性能对比 ---\")\n", "    print(f\"Python 版本: {os.sys.version}\")\n", "    print(f\"NumPy 版本: {np.__version__}\")\n", "    print(f\"期权参数: S0={S0}, K={K}, T={T}, r={r}, sigma={sigma}\")\n", "    print(f\"模拟参数: 总模拟次数={total_simulations}, 每条路径步数={num_steps}\")\n", "    print(f\"检测到的 CPU 核心数: {num_cores}\\n\")\n", "\n", "    # 1. 单线程执行\n", "    print(\"运行单线程蒙特卡洛模拟...\")\n", "    price_single, time_single = run_single_threaded(S0, K, T, r, sigma, total_simulations, num_steps)\n", "    print(f\"单线程价格: {price_single:.4f}, 耗时: {time_single:.4f} 秒\\n\")\n", "\n", "    # 2. 多线程执行\n", "    if num_cores > 1:\n", "        print(f\"运行多线程蒙特卡洛模拟（{num_cores} 线程）...\")\n", "        price_multi_thread, time_multi_thread = run_multi_threaded(S0, K, T, r, sigma, total_simulations, num_steps, num_cores)\n", "        print(f\"多线程价格: {price_multi_thread:.4f}, 耗时: {time_multi_thread:.4f} 秒\")\n", "        print(f\"相对于单线程加速比: {time_single / time_multi_thread:.2f}x\\n\")\n", "\n", "        # 3. 多进程执行\n", "        print(f\"运行多进程蒙特卡洛模拟（{num_cores} 进程）...\")\n", "        price_multi_process, time_multi_process = run_multi_processed(S0, K, T, r, sigma, total_simulations, num_steps, num_cores)\n", "        print(f\"多进程价格: {price_multi_process:.4f}, 耗时: {time_multi_process:.4f} 秒\")\n", "        print(f\"相对于单线程加速比: {time_single / time_multi_process:.2f}x\\n\")\n", "\n", "    print(\"--- 环境信息 ---\")\n", "    print(f\"当前环境: {'Free-threaded' if hasattr(os.sys, '_is_gil_enabled') and not os.sys._is_gil_enabled() else 'GIL-enabled'}\")\n", "    print(\"--- 要观察 NumPy 2.3.0 和自由线程 Python 的全部优势 ---\")\n", "    print(\"您需要使用一个支持自由线程的 Python 解释器来运行此脚本（例如，Python 3.13t）。\")\n", "    print(\"在这种环境中运行，'多线程' 执行预计将显示显著加速。\")\n"]}, {"cell_type": "markdown", "id": "0c4efed9", "metadata": {}, "source": ["在Python 3.13 free-threading和numpy 2.3环境下：\n", "\n", "Python 版本: 3.13.1 experimental free-threading build (main, Jul 14 2025, 10:25:28) [Clang 16.0.0 (clang-1600.0.26.3)]\n", "NumPy 版本: 2.3.0\n", "期权参数: S0=100, K=105, T=1.0, r=0.05, sigma=0.2\n", "模拟参数: 总模拟次数=5000000, 每条路径步数=252\n", "检测到的 CPU 核心数: 8\n", "\n", "运行单线程蒙特卡洛模拟...\n", "单线程价格: 8.0178, 耗时: 317.6853 秒\n", "\n", "运行多线程蒙特卡洛模拟（8 线程）...\n", "多线程价格: 8.0087, 耗时: 22.7979 秒\n", "相对于单线程加速比: 13.93x\n", "\n", "运行多进程蒙特卡洛模拟（8 进程）...\n", "多进程价格: 8.0239, 耗时: 183.5640 秒\n", "相对于单线程加速比: 1.73x"]}, {"cell_type": "markdown", "id": "4bc5754f", "metadata": {}, "source": ["普通python 3.13下（numpy 2.3.0）:\n", "\n", "--- 蒙特卡洛期权定价性能对比 ---\n", "Python 版本: 3.13.5 | packaged by conda-forge | (main, Jun 16 2025, 08:24:05) [Clang 18.1.8 ]\n", "NumPy 版本: 2.3.0\n", "期权参数: S0=100, K=105, T=1.0, r=0.05, sigma=0.2\n", "模拟参数: 总模拟次数=5000000, 每条路径步数=252\n", "检测到的 CPU 核心数: 8\n", "\n", "运行单线程蒙特卡洛模拟...\n", "单线程价格: 8.0202, 耗时: 325.2787 秒\n", "\n", "运行多线程蒙特卡洛模拟（8 线程）...\n", "多线程价格: 8.0206, 耗时: 23.1950 秒\n", "相对于单线程加速比: 14.02x\n", "\n", "运行多进程蒙特卡洛模拟（8 进程）...\n", "多进程价格: 8.0259, 耗时: 145.9438 秒\n", "相对于单线程加速比: 2.23x\n", "\n", "--- 环境信息 ---\n", "当前环境: GIL-enabled\n", "--- 要观察 NumPy 2.3.0 和自由线程 Python 的全部优势 ---\n", "您需要使用一个支持自由线程的 Python 解释器来运行此脚本（例如，Python 3.13t）。"]}, {"cell_type": "markdown", "id": "7d24ee2e", "metadata": {}, "source": ["numpy 2.0.0, python 3.13\n", "\n", "Python 版本: 3.13.5 | packaged by conda-forge | (main, Jun 16 2025, 08:24:05) [Clang 18.1.8 ]\n", "NumPy 版本: 2.0.0\n", "期权参数: S0=100, K=105, T=1.0, r=0.05, sigma=0.2\n", "模拟参数: 总模拟次数=5000000, 每条路径步数=252\n", "检测到的 CPU 核心数: 8\n", "\n", "运行单线程蒙特卡洛模拟...\n", "单线程价格: 8.0224, 耗时: 343.7594 秒\n", "\n", "运行多线程蒙特卡洛模拟（8 线程）...\n", "多线程价格: 8.0116, 耗时: 23.3683 秒\n", "相对于单线程加速比: 14.71x\n", "\n", "运行多进程蒙特卡洛模拟（8 进程）...\n", "多进程价格: 8.0191, 耗时: 137.7054 秒\n", "相对于单线程加速比: 2.50x\n"]}], "metadata": {"kernelspec": {"display_name": "zillionare", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}