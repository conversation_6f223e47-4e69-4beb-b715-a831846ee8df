def get_price(symbol, start_date, end_date):
    pro = pro_api()

    price_df = pro.index_daily(
        ts_code=symbol,
        start_date=start_date.strftime("%Y%m%d"),
        end_date=end_date.strftime("%Y%m%d"),
    )

    price_df = (
        price_df.rename({"trade_date": "date", "ts_code": "asset"}, axis=1)
        .sort_values("date", ascending=True)
        .set_index("date")
    )

    return price_df


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt


def calculate_llt(prices, alpha=0.05):
    """
    计算LLT (Linearly Weighted Least Squares Triangular) 均线

    参数:
    prices (array-like): 价格序列
    alpha (float): 平滑系数，范围(0,1)，值越小均线越平滑，滞后性越大

    返回:
    array: LLT均线序列
    """
    n = len(prices)
    llt = np.zeros(n)

    # 初始化前两个值
    if n >= 1:
        llt[0] = prices[0]
    if n >= 2:
        llt[1] = prices[1]

    # 计算系数
    a1 = alpha - (alpha**2) / 4
    a2 = (alpha**2) / 2
    a3 = alpha - 3 * (alpha**2) / 4
    a4 = 2 * (1 - alpha)
    a5 = -((1 - alpha) ** 2)

    # 递归计算LLT
    for t in range(2, n):
        llt[t] = (
            a1 * prices[t]
            + a2 * prices[t - 1]
            - a3 * prices[t - 2]
            + a4 * llt[t - 1]
            + a5 * llt[t - 2]
        )

    return llt


def backtest(df, calc_signal, args, price: str = "open", long_weight: float = 0.5, short_weight: float = 0.5, long_short=True):
    """原始回测函数"""
    df = df.copy()
    df["signal"] = calc_signal(df, *args)
    df["signal"] = df["signal"].fillna(0)
    df["signal_shifted"] = df["signal"].shift(1)
    df["benchmark"] = df[price].pct_change()
    
    df['long_return'] = np.where(df['signal_shifted'] == 1, df['benchmark'], 0)
    df['short_return'] = np.where(df['signal_shifted'] == -1, -df['benchmark'], 0)
    df["long_short_return"] = df['long_return'] * long_weight + df['short_return'] * short_weight
    
    return df

def ma_slope_signal(df, window=30, slope_window=5):
    df = df.copy()
    ma = df["close"].rolling(window).mean()
    df['slope'] = (ma.rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals

def llt_slope_signal(df, d: int=30, slope_window=5):
    df = df.copy()
    alpha = 2 / (d + 1)
    df["llt"] = calculate_llt(df["close"], alpha)
    df['slope'] = (df["llt"].rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals


start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)
prices= get_price("000001.SH", start, end)


result_df = backtest(prices, ma_slope_signal, (30, 5), price='open')
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

result_df = backtest(prices, llt_slope_signal, (5, ), price='open')
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Callable, Tuple, Any

def backtest(df, calc_signal, args, price: str = "open", long_weight: float = 0.5, short_weight: float = 0.5, long_short=True):
    """原始回测函数"""
    df = df.copy()
    df["signal"] = calc_signal(df, *args)
    df["signal"] = df["signal"].fillna(0)
    df["signal_shifted"] = df["signal"].shift(1)
    df["benchmark"] = df[price].pct_change()
    
    df['long_return'] = np.where(df['signal_shifted'] == 1, df['benchmark'], 0)
    df['short_return'] = np.where(df['signal_shifted'] == -1, -df['benchmark'], 0)
    df["long_short_return"] = df['long_return'] * long_weight + df['short_return'] * short_weight
    
    return df

def calculate_metrics(returns: pd.Series, benchmark_returns: pd.Series) -> dict:
    """计算策略表现指标"""
    # 基本统计
    total_return = (1 + returns).prod() - 1
    benchmark_total = (1 + benchmark_returns).prod() - 1
    
    # 年化收益率
    trading_days = len(returns)
    annual_return = (1 + total_return) ** (252 / trading_days) - 1
    benchmark_annual = (1 + benchmark_total) ** (252 / trading_days) - 1
    
    # 胜率
    win_rate = (returns > 0).mean()
    benchmark_win_rate = (benchmark_returns > 0).mean()
    
    # 波动率
    volatility = returns.std() * np.sqrt(252)
    benchmark_vol = benchmark_returns.std() * np.sqrt(252)
    
    # 夏普比率
    sharpe = annual_return / volatility if volatility > 0 else 0
    benchmark_sharpe = benchmark_annual / benchmark_vol if benchmark_vol > 0 else 0
    
    # 最大回撤
    cumulative = (1 + returns).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdown = (cumulative - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    benchmark_cum = (1 + benchmark_returns).cumprod()
    benchmark_rolling_max = benchmark_cum.expanding().max()
    benchmark_drawdown = (benchmark_cum - benchmark_rolling_max) / benchmark_rolling_max
    benchmark_max_dd = benchmark_drawdown.min()
    
    # Sortino比率
    downside_returns = returns[returns < 0]
    downside_vol = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
    sortino = annual_return / downside_vol if downside_vol > 0 else 0
    
    benchmark_downside = benchmark_returns[benchmark_returns < 0]
    benchmark_downside_vol = benchmark_downside.std() * np.sqrt(252) if len(benchmark_downside) > 0 else 0
    benchmark_sortino = benchmark_annual / benchmark_downside_vol if benchmark_downside_vol > 0 else 0
    
    return {
        '策略': {
            '年化收益率': f"{annual_return:.2%}",
            '胜率': f"{win_rate:.2%}",
            '夏普比率': f"{sharpe:.3f}",
            '最大回撤': f"{max_drawdown:.2%}",
            'Sortino比率': f"{sortino:.3f}"
        },
        'Benchmark': {
            '年化收益率': f"{benchmark_annual:.2%}",
            '胜率': f"{benchmark_win_rate:.2%}",
            '夏普比率': f"{benchmark_sharpe:.3f}",
            '最大回撤': f"{benchmark_max_dd:.2%}",
            'Sortino比率': f"{benchmark_sortino:.3f}"
        }
    }

def create_backtest_report(df, calc_signal, args, price: str = "open", 
                          long_weight: float = 0.5, short_weight: float = 0.5, 
                          long_short: bool = True, title: str = "回测报告") -> go.Figure:
    """创建完整的回测报告"""
    
    # 执行回测
    result_df = backtest(df, calc_signal, args, price, long_weight, short_weight, long_short)
    
    # 选择策略收益
    if long_short:
        strategy_col = "long_short_return"
    else:
        strategy_col = 'long_return'
    
    # 去除NaN值
    valid_data = result_df.dropna(subset=[strategy_col, 'benchmark'])
    strategy_returns = valid_data[strategy_col]
    benchmark_returns = valid_data['benchmark']
    signals = valid_data['signal_shifted']
    prices = valid_data[price]
    
    # 计算累积收益
    strategy_cumulative = (1 + strategy_returns).cumprod()
    benchmark_cumulative = (1 + benchmark_returns).cumprod()
    
    # 计算指标
    metrics = calculate_metrics(strategy_returns, benchmark_returns)
    
    # 处理时间索引
    if isinstance(valid_data.index, pd.DatetimeIndex):
        # 创建分类索引以去掉非交易日
        x_axis = list(range(len(valid_data)))
        x_labels = [d.strftime('%Y-%m-%d') for d in valid_data.index]
        x_tickvals = list(range(0, len(valid_data), max(1, len(valid_data)//10)))
        x_ticktext = [x_labels[i] for i in x_tickvals]
    else:
        x_axis = valid_data.index
        x_labels = [str(x) for x in x_axis]
        x_tickvals = x_axis
        x_ticktext = x_labels
    
    # 创建子图
    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=['累积收益对比', '策略指标对比'],
        vertical_spacing=0.15,
        row_heights=[0.7, 0.3],
        specs=[[{"secondary_y": False}], [{"type": "table"}]]
    )
    
    # 第一个子图：累积收益曲线
    fig.add_trace(
        go.Scatter(
            x=x_axis,
            y=strategy_cumulative,
            name='策略收益',
            line=dict(color='blue', width=2),
            hovertemplate='日期: %{text}<br>累积收益: %{y:.2%}<extra></extra>',
            text=x_labels
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=x_axis,
            y=benchmark_cumulative,
            name='基准收益',
            line=dict(color='gray', width=1, dash='dash'),
            hovertemplate='日期: %{text}<br>基准收益: %{y:.2%}<extra></extra>',
            text=x_labels
        ),
        row=1, col=1
    )
    
    # 过滤连续信号，只保留每组连续信号的第一个
    def filter_consecutive_signals(signal_series, target_value):
        """过滤连续信号，只保留每组连续信号的第一个"""
        # 使用向量化操作：检测信号变化点
        mask = (signal_series == target_value) & (signal_series.shift(1) != target_value)
        # 返回整数索引位置
        return [i for i, val in enumerate(mask) if val]
    
    # 添加买卖标记
    buy_indices = filter_consecutive_signals(signals, 1)
    sell_indices = filter_consecutive_signals(signals, -1)
    
    if buy_indices:
        buy_prices = [strategy_cumulative.iloc[i] for i in buy_indices]
        buy_dates = [x_labels[i] for i in buy_indices]
        buy_actual_prices = [prices.iloc[i] for i in buy_indices]
        
        fig.add_trace(
            go.Scatter(
                x=buy_indices,
                y=buy_prices,
                mode='markers',
                name='买入信号',
                marker=dict(symbol='triangle-up', size=10, color='green'),
                hovertemplate='买入<br>日期: %{text}<br>价格: %{customdata:.2f}<br>累积收益: %{y:.2%}<extra></extra>',
                text=buy_dates,
                customdata=buy_actual_prices
            ),
            row=1, col=1
        )
    
    if sell_indices:
        sell_prices = [strategy_cumulative.iloc[i] for i in sell_indices]
        sell_dates = [x_labels[i] for i in sell_indices]
        sell_actual_prices = [prices.iloc[i] for i in sell_indices]
        
        fig.add_trace(
            go.Scatter(
                x=sell_indices,
                y=sell_prices,
                mode='markers',
                name='卖出信号',
                marker=dict(symbol='triangle-down', size=10, color='red'),
                hovertemplate='卖出<br>日期: %{text}<br>价格: %{customdata:.2f}<br>累积收益: %{y:.2%}<extra></extra>',
                text=sell_dates,
                customdata=sell_actual_prices
            ),
            row=1, col=1
        )
    
    # 第二个子图：指标对比表格
    metrics_data = []
    for metric in ['年化收益率', '胜率', '夏普比率', '最大回撤', 'Sortino比率']:
        metrics_data.append([metric, metrics['策略'][metric], metrics['Benchmark'][metric]])
    
    fig.add_trace(
        go.Table(
            header=dict(
                values=['指标', '策略', 'Benchmark'],
                fill_color='lightblue',
                align='center',
                font=dict(size=12, color='black')
            ),
            cells=dict(
                values=list(zip(*metrics_data)),
                fill_color='white',
                align='center',
                font=dict(size=11)
            )
        ),
        row=2, col=1
    )
    
    # 更新布局
    fig.update_layout(
        title=dict(
            text=title,
            x=0.5,
            font=dict(size=16)
        ),
        height=800,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    # 更新x轴
    fig.update_xaxes(
        title_text="交易日",
        tickvals=x_tickvals,
        ticktext=x_ticktext,
        tickangle=45,
        row=1, col=1
    )
    
    # 更新y轴
    fig.update_yaxes(
        title_text="累积收益率",
        tickformat=".1%",
        row=1, col=1
    )
    
    return fig

# 示例信号函数
def simple_ma_signal(df: pd.DataFrame, short_window: int = 5, long_window: int = 20) -> pd.Series:
    """简单移动平均交叉信号"""
    short_ma = df['close'].rolling(window=short_window).mean()
    long_ma = df['close'].rolling(window=long_window).mean()
    
    signals = pd.Series(0, index=df.index)
    signals[short_ma > long_ma] = 1
    signals[short_ma < long_ma] = -1
    
    return signals

def ma_slope_signa(df, window=30, slope_window=5):
    df = df.copy()
    ma = df["close"].rolling(window).mean()
    df['slope'] = (ma.rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals

def llt_slope_signal(df, d: int=30, slope_window=5):
    df = df.copy()
    alpha = 2 / (d + 1)
    df["llt"] = calculate_llt(df["close"], alpha)
    df['slope'] = (df["llt"].rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals


start = datetime.date(2012, 9, 6)
end = datetime.date(2013, 6, 28)
prices= get_price("000001.SH", start, end)

# 创建报告
fig = create_backtest_report(
    prices, 
    ma_slope_signa, 
    (5, 20),  # 参数
    price='close',
    long_short=True,
    title="移动平均策略回测报告"
)

fig.show()

fig = create_backtest_report(
    prices, 
    llt_slope_signal, 
    (5, 20),  # 参数
    price='close',
    long_short=True,
    title="LLT切线买入法"
)

fig.show()