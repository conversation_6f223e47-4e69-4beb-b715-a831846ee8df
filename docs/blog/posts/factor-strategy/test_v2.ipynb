def get_price(symbol, start_date, end_date):
    pro = pro_api()

    price_df = pro.index_daily(
        ts_code=symbol,
        start_date=start_date.strftime("%Y%m%d"),
        end_date=end_date.strftime("%Y%m%d"),
    )

    price_df = (
        price_df.rename({"trade_date": "date", "ts_code": "asset"}, axis=1)
        .sort_values("date", ascending=True)
        .set_index("date")
    )

    return price_df


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt


def calculate_llt(prices, alpha=0.05):
    """
    计算LLT (Linearly Weighted Least Squares Triangular) 均线

    参数:
    prices (array-like): 价格序列
    alpha (float): 平滑系数，范围(0,1)，值越小均线越平滑，滞后性越大

    返回:
    array: LLT均线序列
    """
    n = len(prices)
    llt = np.zeros(n)

    # 初始化前两个值
    if n >= 1:
        llt[0] = prices[0]
    if n >= 2:
        llt[1] = prices[1]

    # 计算系数
    a1 = alpha - (alpha**2) / 4
    a2 = (alpha**2) / 2
    a3 = alpha - 3 * (alpha**2) / 4
    a4 = 2 * (1 - alpha)
    a5 = -((1 - alpha) ** 2)

    # 递归计算LLT
    for t in range(2, n):
        llt[t] = (
            a1 * prices[t]
            + a2 * prices[t - 1]
            - a3 * prices[t - 2]
            + a4 * llt[t - 1]
            + a5 * llt[t - 2]
        )

    return llt


def backtest(df, calc_signal, args, price: str = "open", long_weight: float = 0.5, short_weight: float = 0.5, long_short=True):
    """原始回测函数"""
    df = df.copy()
    df["signal"] = calc_signal(df, *args)
    df["signal"] = df["signal"].fillna(0)
    df["signal_shifted"] = df["signal"].shift(1)
    df["benchmark"] = df[price].pct_change()
    
    df['long_return'] = np.where(df['signal_shifted'] == 1, df['benchmark'], 0)
    df['short_return'] = np.where(df['signal_shifted'] == -1, -df['benchmark'], 0)
    df["long_short_return"] = df['long_return'] * long_weight + df['short_return'] * short_weight
    
    return df

def ma_slope_signal(df, window=30, slope_window=5):
    df = df.copy()
    ma = df["close"].rolling(window).mean()
    df['slope'] = (ma.rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals

def llt_slope_signal(df, d: int=30, slope_window=5):
    df = df.copy()
    alpha = 2 / (d + 1)
    df["llt"] = calculate_llt(df["close"], alpha)
    df['slope'] = (df["llt"].rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals


start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)
prices= get_price("000001.SH", start, end)


result_df = backtest(prices, ma_slope_signal, (30, 5))
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

result_df = backtest(prices, llt_slope_signal, (5, ))
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

result_df = backtest(prices, ma_slope_signal, (5, ), price="open")
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

import pandas as pd
import numpy as np

dates = pd.date_range('2023-01-01', periods=10, freq='D')

base_price = 100
growth_rate = np.array([0.05, -0.05] * int(len(dates)/2))

prices = np.array(base_price * np.cumprod((1 + growth_rate)))

df = pd.DataFrame({
    'open': prices,
}, index=dates)

print("价格数据:")
print(df[['open']].round(2))

def simple_fixed_signal(df):
    """返回固定的信号序列，便于手工验证"""
    signals = pd.Series([
        0,   # 第1天：无信号
        1,   # 第2天：买入信号
        1,   # 第3天：持有
        0,   # 第4天：平仓
        -1,  # 第5天：卖出信号
        -1,  # 第6天：持有
        -1,   # 第7天：持有
        -1,   # 第8天：持有
        0,   # 第9天：平仓
        0    # 第10天：无信号
    ], index=df.index)
    
    return signals

result_df = backtest(df, simple_fixed_signal, ())
result_df



start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)
prices= get_price("000001.SH", start, end)
result_df = run_backtrader_llt(prices, alpha=0.05, slope_window=5)
result_df



import backtrader as bt

class SlopeIndicator(bt.Indicator):
    """斜率指标 - 计算指定周期内的线性回归斜率"""
    lines = ('slope',)
    params = (('period', 5),)
    
    def __init__(self):
        self.addminperiod(self.p.period)
        
    def next(self):
        # 获取最近period个数据点
        y_values = [self.data[i] for i in range(-self.p.period + 1, 1)]
        x_values = list(range(self.p.period))
        
        # 计算线性回归斜率
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        
        # 斜率公式: slope = (n*sum_xy - sum_x*sum_y) / (n*sum_x2 - sum_x*sum_x)
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator != 0:
            slope = (n * sum_xy - sum_x * sum_y) / denominator
        else:
            slope = 0
            
        self.l.slope[0] = slope


class LLTIndicator(bt.Indicator):
    """LLT (Low Lag Trend) 指标"""
    lines = ('llt',)
    params = (('alpha', 0.05),)
    
    def __init__(self):
        self.addminperiod(3)  # 至少需要3个数据点
        
    def next(self):
        if len(self.data) < 3:
            self.l.llt[0] = self.data.close[0]
            return
            
        # LLT计算系数
        alpha = self.p.alpha
        a1 = alpha - (alpha**2) / 4
        a2 = (alpha**2) / 2
        a3 = alpha - 3 * (alpha**2) / 4
        a4 = 2 * (1 - alpha)
        a5 = -((1 - alpha) ** 2)
        
        # 递归计算LLT
        if len(self.l.llt) >= 2:
            self.l.llt[0] = (
                a1 * self.data.close[0] +
                a2 * self.data.close[-1] -
                a3 * self.data.close[-2] +
                a4 * self.l.llt[-1] +
                a5 * self.l.llt[-2]
            )
        else:
            self.l.llt[0] = self.data.close[0]


class LLTStrategy(bt.Strategy):
    """基于LLT斜率的交易策略"""
    params = (
        ('alpha', 0.05),
        ('slope_window', 5),
        ('long_weight', 0.5),
        ('short_weight', 0.5),
    )
    
    def __init__(self):
        # 添加LLT指标
        self.llt = LLTIndicator(alpha=self.p.alpha)
        
        # 计算LLT斜率
        self.slope = SlopeIndicator(self.llt.llt, period=self.p.slope_window)
        
        # 生成信号
        self.signal = bt.If(self.slope > 0, 1, bt.If(self.slope < 0, -1, 0))
        
        # 记录变量
        self.order = None
        self.position_size = 0
        self.trade_count = 0
        
    def next(self):
        if self.order:
            return
            
        current_signal = self.signal[0]
        
        # 多头信号
        if current_signal == 1 and self.position_size <= 0:
            if self.position_size < 0:
                # 平空仓
                self.order = self.close()
            else:
                # 开多仓
                size = self.broker.get_cash() * self.p.long_weight / self.data.close[0]
                self.order = self.buy(size=size)
                self.position_size = 1
                
        # 空头信号  
        elif current_signal == -1 and self.position_size >= 0:
            if self.position_size > 0:
                # 平多仓
                self.order = self.close()
            else:
                # 开空仓
                size = self.broker.get_cash() * self.p.short_weight / self.data.close[0]
                self.order = self.sell(size=size)
                self.position_size = -1
                
    def notify_order(self, order):
        if order.status in [order.Completed]:
            self.order = None
            
    def notify_trade(self, trade):
        if trade.isclosed:
            self.position_size = 0
            self.trade_count += 1


def run_backtrader_llt(data_df, alpha=0.05, slope_window=5, initial_cash=100000):
    """运行backtrader LLT策略回测"""
    
    # 准备数据
    data_df = data_df.copy()
    data_df.index = pd.to_datetime(data_df.index)
    
    # 创建backtrader数据源
    data = bt.feeds.PandasData(
        dataname=data_df,
        datetime=None,
        open='open',
        high='high', 
        low='low',
        close='close',
        volume='vol' if 'vol' in data_df.columns else None,
        openinterest=None
    )
    
    # 创建cerebro引擎
    cerebro = bt.Cerebro()
    
    # 添加策略
    cerebro.addstrategy(LLTStrategy, alpha=alpha, slope_window=slope_window)
    
    # 添加数据
    cerebro.adddata(data)
    
    # 设置初始资金
    cerebro.broker.setcash(initial_cash)
    
    # 设置手续费
    cerebro.broker.setcommission(commission=0.001)
    
    # 运行回测
    print(f'初始资金: {cerebro.broker.getvalue():.2f}')
    result = cerebro.run()
    final_value = cerebro.broker.getvalue()
    print(f'最终资金: {final_value:.2f}')
    
    # 计算收益率
    total_return = (final_value - initial_cash) / initial_cash
    print(f'总收益率: {total_return:.2%}')
    
    # 计算基准收益率
    benchmark_return = (data_df['close'].iloc[-1] - data_df['close'].iloc[0]) / data_df['close'].iloc[0]
    print(f'基准收益率: {benchmark_return:.2%}')
    print(f'超额收益: {total_return - benchmark_return:.2%}')
    
    # 获取策略实例
    strategy = result[0]
    print(f'交易次数: {strategy.trade_count}')
    
    return {
        'initial_cash': initial_cash,
        'final_value': final_value,
        'total_return': total_return,
        'benchmark_return': benchmark_return,
        'excess_return': total_return - benchmark_return,
        'trade_count': strategy.trade_count,
        'strategy': strategy
    }

start = 
result = run_backtrader_llt(prices, alpha=0.05, slope_window=5)
