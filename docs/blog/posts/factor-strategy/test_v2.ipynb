def get_price(symbol, start_date, end_date):
    pro = pro_api()

    price_df = pro.index_daily(
        ts_code=symbol,
        start_date=start_date.strftime("%Y%m%d"),
        end_date=end_date.strftime("%Y%m%d"),
    )

    price_df = (
        price_df.rename({"trade_date": "date", "ts_code": "asset"}, axis=1)
        .sort_values("date", ascending=True)
        .set_index("date")
    )

    return price_df


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt


def calculate_llt(prices, alpha=0.05):
    """
    计算LLT (Linearly Weighted Least Squares Triangular) 均线

    参数:
    prices (array-like): 价格序列
    alpha (float): 平滑系数，范围(0,1)，值越小均线越平滑，滞后性越大

    返回:
    array: LLT均线序列
    """
    n = len(prices)
    llt = np.zeros(n)

    # 初始化前两个值
    if n >= 1:
        llt[0] = prices[0]
    if n >= 2:
        llt[1] = prices[1]

    # 计算系数
    a1 = alpha - (alpha**2) / 4
    a2 = (alpha**2) / 2
    a3 = alpha - 3 * (alpha**2) / 4
    a4 = 2 * (1 - alpha)
    a5 = -((1 - alpha) ** 2)

    # 递归计算LLT
    for t in range(2, n):
        llt[t] = (
            a1 * prices[t]
            + a2 * prices[t - 1]
            - a3 * prices[t - 2]
            + a4 * llt[t - 1]
            + a5 * llt[t - 2]
        )

    return llt


def backtest(df, calc_signal, args, price: str = "open", long_weight: float = 0.5, short_weight: float = 0.5, long_short=True):
    """原始回测函数"""
    df = df.copy()
    df["signal"] = calc_signal(df, *args)
    df["signal"] = df["signal"].fillna(0)
    df["signal_shifted"] = df["signal"].shift(1)
    df["benchmark"] = df[price].pct_change()
    
    df['long_return'] = np.where(df['signal_shifted'] == 1, df['benchmark'], 0)
    df['short_return'] = np.where(df['signal_shifted'] == -1, -df['benchmark'], 0)
    df["long_short_return"] = df['long_return'] * long_weight + df['short_return'] * short_weight
    
    return df

def ma_slope_signal(df, window=30, slope_window=5):
    df = df.copy()
    ma = df["close"].rolling(window).mean()
    df['slope'] = (ma.rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals

def llt_slope_signal(df, d: int=30, slope_window=5):
    df = df.copy()
    alpha = 2 / (d + 1)
    df["llt"] = calculate_llt(df["close"], alpha)
    df['slope'] = (df["llt"].rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals


start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)
prices= get_price("000001.SH", start, end)


result_df = backtest(prices, ma_slope_signal, (30, 5))
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

result_df = backtest(prices, llt_slope_signal, (5, ))
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

result_df = backtest(prices, ma_slope_signal, (5, ), price="open")
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

import pandas as pd
import numpy as np

dates = pd.date_range('2023-01-01', periods=10, freq='D')

base_price = 100
growth_rate = np.array([0.05, -0.05] * int(len(dates)/2))

prices = np.array(base_price * np.cumprod((1 + growth_rate)))

df = pd.DataFrame({
    'open': prices,
}, index=dates)

print("价格数据:")
print(df[['open']].round(2))

def simple_fixed_signal(df):
    """返回固定的信号序列，便于手工验证"""
    signals = pd.Series([
        0,   # 第1天：无信号
        1,   # 第2天：买入信号
        1,   # 第3天：持有
        0,   # 第4天：平仓
        -1,  # 第5天：卖出信号
        -1,  # 第6天：持有
        -1,   # 第7天：持有
        -1,   # 第8天：持有
        0,   # 第9天：平仓
        0    # 第10天：无信号
    ], index=df.index)
    
    return signals

result_df = backtest(df, simple_fixed_signal, ())
result_df



start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)
prices= get_price("000001.SH", start, end)
result_df = run_backtrader_llt(prices, alpha=0.05, slope_window=5)
result_df



import backtrader as bt
import numpy as np
import pandas as pd
from datetime import datetime

class LLTIndicator(bt.Indicator):
    """自定义LLT指标"""
    lines = ('llt',)  # 输出线：LLT值
    params = (
        ('alpha', 0.05),  # 平滑系数α
    )
    
    def __init__(self):
        super(LLTIndicator, self).__init__()
        # 计算LLT系数
        alpha = self.p.alpha
        self.a1 = alpha - (alpha**2) / 4
        self.a2 = (alpha**2) / 2
        self.a3 = alpha - 3 * (alpha**2) / 4
        self.a4 = 2 * (1 - alpha)
        self.a5 = - (1 - alpha)** 2
    
    def next(self):
        # 初始化前2个值
        if len(self) == 1:
            self.lines.llt[0] = self.data.close[0]  # 第1个值=收盘价
        elif len(self) == 2:
            self.lines.llt[0] = self.data.close[1]  # 第2个值=前一天收盘价
        else:
            # 递归计算LLT（使用前2期LLT和最近3期收盘价）
            self.lines.llt[0] = (
                self.a1 * self.data.close[0]  # 当前收盘价
                + self.a2 * self.data.close[-1]  # 前1期收盘价
                - self.a3 * self.data.close[-2]  # 前2期收盘价
                + self.a4 * self.lines.llt[-1]  # 前1期LLT
                + self.a5 * self.lines.llt[-2]  # 前2期LLT
            )


# 2. 定义策略类
class LLTSlopeStrategy(bt.Strategy):
    """基于LLT斜率的交易策略"""
    params = (
        ('alpha', 0.05),  # LLT的平滑系数
        ('slope_window', 5),  # 计算斜率的窗口（5日）
    )
    
    def __init__(self):
        # 计算LLT指标
        self.llt = LLTIndicator(self.data, alpha=self.p.alpha)
        
        # 计算LLT的5日斜率（用线性回归）
        self.slope = bt.indicators.LinearRegressionSlope(
            self.llt,
            period=self.p.slope_window
        )
        
        # 记录交易状态
        self.order = None  # 订单
        self.position_size = 100  # 假设每次交易100股（可调整）
    
    def next(self):
        # 无持仓且斜率>0 → 买入
        if not self.position and self.slope[0] > 0:
            self.order = self.buy(size=self.position_size)
            print(f"买入：日期={self.data.datetime.date(0)}, 价格={self.data.close[0]:.2f}")
        
        # 有持仓且斜率<0 → 卖出
        elif self.position and self.slope[0] < 0:
            self.order = self.sell(size=self.position_size)
            print(f"卖出：日期={self.data.datetime.date(0)}, 价格={self.data.close[0]:.2f}")
    
    def notify_order(self, order):
        # 订单状态处理（简化版）
        if order.status in [order.Completed]:
            self.order = None  # 重置订单


# 3. 回测主函数
def run_backtest(data, start_date, end_date, alpha=0.05):
    # 初始化引擎
    cerebro = bt.Cerebro()
    
    # 添加策略
    cerebro.addstrategy(LLTSlopeStrategy, alpha=alpha)
    
    cerebro.adddata(data)
    
    # 初始资金
    cerebro.broker.setcash(100000.0)
    # 佣金（0.1%）
    cerebro.broker.setcommission(commission=0.001)
    
    # 运行回测
    print(f"初始资金: {cerebro.broker.getvalue():.2f}")
    cerebro.run()
    print(f"最终资金: {cerebro.broker.getvalue():.2f}")
    
    # 绘制回测结果
    cerebro.plot(style='candlestick')

start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)

prices = get_price("000001.SH", start, end)
result = run_backtrader_llt(prices, alpha=0.05, slope_window=5)
