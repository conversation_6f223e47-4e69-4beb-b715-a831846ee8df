def get_price(symbol, start_date, end_date):
    pro = pro_api()

    price_df = pro.index_daily(
        ts_code=symbol,
        start_date=start_date.strftime("%Y%m%d"),
        end_date=end_date.strftime("%Y%m%d"),
    )

    price_df = (
        price_df.rename({"trade_date": "date", "ts_code": "asset"}, axis=1)
        .sort_values("date", ascending=True)
        .set_index("date")
    )

    return price_df


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt


def calculate_llt(prices, alpha=0.05):
    """
    计算LLT (Linearly Weighted Least Squares Triangular) 均线

    参数:
    prices (array-like): 价格序列
    alpha (float): 平滑系数，范围(0,1)，值越小均线越平滑，滞后性越大

    返回:
    array: LLT均线序列
    """
    n = len(prices)
    llt = np.zeros(n)

    # 初始化前两个值
    if n >= 1:
        llt[0] = prices[0]
    if n >= 2:
        llt[1] = prices[1]

    # 计算系数
    a1 = alpha - (alpha**2) / 4
    a2 = (alpha**2) / 2
    a3 = alpha - 3 * (alpha**2) / 4
    a4 = 2 * (1 - alpha)
    a5 = -((1 - alpha) ** 2)

    # 递归计算LLT
    for t in range(2, n):
        llt[t] = (
            a1 * prices[t]
            + a2 * prices[t - 1]
            - a3 * prices[t - 2]
            + a4 * llt[t - 1]
            + a5 * llt[t - 2]
        )

    return llt


def backtest(df, calc_signal, args, price: str = "open", long_weight: float = 0.5, short_weight: float = 0.5, long_short=True):
    """原始回测函数"""
    df = df.copy()
    df["signal"] = calc_signal(df, *args)
    df["signal"] = df["signal"].fillna(0)
    df["signal_shifted"] = df["signal"].shift(1)
    df["benchmark"] = df[price].pct_change()
    
    df['long_return'] = np.where(df['signal_shifted'] == 1, df['benchmark'], 0)
    df['short_return'] = np.where(df['signal_shifted'] == -1, -df['benchmark'], 0)
    df["long_short_return"] = df['long_return'] * long_weight + df['short_return'] * short_weight
    
    return df

def ma_slope_signal(df, window=30, slope_window=5):
    df = df.copy()
    ma = df["close"].rolling(window).mean()
    df['slope'] = (ma.rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals

def llt_slope_signal(df, d: int=30, slope_window=5):
    df = df.copy()
    alpha = 2 / (d + 1)
    df["llt"] = calculate_llt(df["close"], alpha)
    df['slope'] = (df["llt"].rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals


start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)
prices= get_price("000001.SH", start, end)


result_df = backtest(prices, ma_slope_signal, (30, 5))
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

result_df = backtest(prices, llt_slope_signal, (5, ))
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

result_df = backtest(prices, ma_slope_signal, (5, ), price="open")
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

import pandas as pd
import numpy as np

dates = pd.date_range('2023-01-01', periods=10, freq='D')

base_price = 100
growth_rate = np.array([0.05, -0.05] * int(len(dates)/2))

prices = np.array(base_price * np.cumprod((1 + growth_rate)))

df = pd.DataFrame({
    'open': prices,
}, index=dates)

print("价格数据:")
print(df[['open']].round(2))

def simple_fixed_signal(df):
    """返回固定的信号序列，便于手工验证"""
    signals = pd.Series([
        0,   # 第1天：无信号
        1,   # 第2天：买入信号
        1,   # 第3天：持有
        0,   # 第4天：平仓
        -1,  # 第5天：卖出信号
        -1,  # 第6天：持有
        -1,   # 第7天：持有
        -1,   # 第8天：持有
        0,   # 第9天：平仓
        0    # 第10天：无信号
    ], index=df.index)
    
    return signals

result_df = backtest(df, simple_fixed_signal, ())
result_df

(1 + result_df["long_return"]).cumprod() - 1