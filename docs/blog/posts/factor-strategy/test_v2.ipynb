def get_price(symbol, start_date, end_date):
    pro = pro_api()

    price_df = pro.index_daily(
        ts_code=symbol,
        start_date=start_date.strftime("%Y%m%d"),
        end_date=end_date.strftime("%Y%m%d"),
    )

    price_df = (
        price_df.rename({"trade_date": "date", "ts_code": "asset"}, axis=1)
        .sort_values("date", ascending=True)
        .set_index("date")
    )

    return price_df


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt


def calculate_llt(prices, alpha=0.05):
    """
    计算LLT (Linearly Weighted Least Squares Triangular) 均线

    参数:
    prices (array-like): 价格序列
    alpha (float): 平滑系数，范围(0,1)，值越小均线越平滑，滞后性越大

    返回:
    array: LLT均线序列
    """
    n = len(prices)
    llt = np.zeros(n)

    # 初始化前两个值
    if n >= 1:
        llt[0] = prices[0]
    if n >= 2:
        llt[1] = prices[1]

    # 计算系数
    a1 = alpha - (alpha**2) / 4
    a2 = (alpha**2) / 2
    a3 = alpha - 3 * (alpha**2) / 4
    a4 = 2 * (1 - alpha)
    a5 = -((1 - alpha) ** 2)

    # 递归计算LLT
    for t in range(2, n):
        llt[t] = (
            a1 * prices[t]
            + a2 * prices[t - 1]
            - a3 * prices[t - 2]
            + a4 * llt[t - 1]
            + a5 * llt[t - 2]
        )

    return llt


def backtest(df, calc_signal, args, price: str = "open", long_weight: float = 0.5, short_weight: float = 0.5, long_short=True):
    """原始回测函数"""
    df = df.copy()
    df["signal"] = calc_signal(df, *args)
    df["signal"] = df["signal"].fillna(0)
    df["signal_shifted"] = df["signal"].shift(1)
    df["benchmark"] = df[price].pct_change()
    
    df['long_return'] = np.where(df['signal_shifted'] == 1, df['benchmark'], 0)
    df['short_return'] = np.where(df['signal_shifted'] == -1, -df['benchmark'], 0)
    df["long_short_return"] = df['long_return'] * long_weight + df['short_return'] * short_weight
    
    return df

def ma_slope_signal(df, window=30, slope_window=5):
    df = df.copy()
    ma = df["close"].rolling(window).mean()
    df['slope'] = (ma.rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals

def llt_slope_signal(df, d: int=30, slope_window=5):
    df = df.copy()
    alpha = 2 / (d + 1)
    df["llt"] = calculate_llt(df["close"], alpha)
    df['slope'] = (df["llt"].rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals

start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)
prices= get_price("000001.SH", start, end)

result_df = backtest(prices, ma_slope_signal, (30, 5))
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )



pd.options.display.max_rows = 300

start = datetime.date(2013, 1, 1)
end = datetime.date(2024, 12, 31)

prices= get_price("000001.SH", start, end)
result_df = backtest(prices, llt_slope_signal, (5,))
result_df["signal"].head(299)





result_df = backtest(prices, llt_slope_signal, (5, ))
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

result_df = backtest(prices, ma_slope_signal, (5, ), price="open")
result_df.index = pd.to_datetime(result_df.index)

qs.plots.returns(
    returns=result_df["long_short_return"],
    benchmark=result_df["benchmark"]
)

metrics = qs.reports.metrics(
        returns = result_df["long_short_return"],
        benchmark = result_df["benchmark"],
        display=False
    )

print(metrics[:10])

import pandas as pd
import numpy as np

dates = pd.date_range('2023-01-01', periods=10, freq='D')

base_price = 100
growth_rate = np.array([0.05, -0.05] * int(len(dates)/2))

prices = np.array(base_price * np.cumprod((1 + growth_rate)))

df = pd.DataFrame({
    'open': prices,
}, index=dates)

print("价格数据:")
print(df[['open']].round(2))

def simple_fixed_signal(df):
    """返回固定的信号序列，便于手工验证"""
    signals = pd.Series([
        0,   # 第1天：无信号
        1,   # 第2天：买入信号
        1,   # 第3天：持有
        0,   # 第4天：平仓
        -1,  # 第5天：卖出信号
        -1,  # 第6天：持有
        -1,   # 第7天：持有
        -1,   # 第8天：持有
        0,   # 第9天：平仓
        0    # 第10天：无信号
    ], index=df.index)
    
    return signals

result_df = backtest(df, simple_fixed_signal, ())
result_df



start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)
prices= get_price("000001.SH", start, end)
result_df = run_backtrader_llt(prices, alpha=0.05, slope_window=5)
result_df



import backtrader as bt

def get_price(symbol, start_date, end_date):
    pro = pro_api()

    price_df = pro.index_daily(
        ts_code=symbol,
        start_date=start_date.strftime("%Y%m%d"),
        end_date=end_date.strftime("%Y%m%d"),
    )

    price_df = (
        price_df.rename({"trade_date": "date", "ts_code": "asset"}, axis=1)
        .sort_values("date", ascending=True)
        .set_index("date")
    )

    return price_df

def calculate_llt(prices, alpha=0.05):
    n = len(prices)
    llt = np.zeros(n)
    if n >= 1:
        llt[0] = prices[0]
    if n >= 2:
        llt[1] = prices[1]
    
    a1 = alpha - (alpha**2) / 4
    a2 = (alpha**2) / 2
    a3 = alpha - 3 * (alpha**2) / 4
    a4 = 2 * (1 - alpha)
    a5 = - (1 - alpha)**2
    
    for t in range(2, n):
        llt[t] = a1 * prices[t] + a2 * prices[t-1] - a3 * prices[t-2] + a4 * llt[t-1] + a5 * llt[t-2]
    
    return llt

# 您提供的信号计算函数
def llt_slope_signal(df, d: int=30, slope_window=5):
    df = df.copy()
    alpha = 2 / (d + 1)
    df["llt"] = calculate_llt(df["close"], alpha)
    df['slope'] = (df["llt"].rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals

class LongShortLLTStrategy(bt.Strategy):
    params = (
        ('d', 30),                # LLT参数d
        ('slope_window', 5),      # 斜率计算窗口
        ('long_weight', 0.5),     # 多头权重
        ('short_weight', 0.5),    # 空头权重
        ('volatility_period', 20) # 波动率计算周期
    )
    
    def __init__(self):
        # 预计算信号
        self.signals = llt_slope_signal(
            self.data._dataname,  # 访问原始DataFrame
            d=self.p.d,
            slope_window=self.p.slope_window
        )
        
        # 确保信号索引与回测日期对齐
        self.signals = self.signals.tz_localize(None)  # 移除时区信息
        
        # 计算波动率（用于仓位控制）
        self.volatility = bt.indicators.StdDev(
            self.data.close, 
            period=self.p.volatility_period
        )
        
        # 初始化多空仓位和订单
        self.long_position = 0   # 多头仓位大小
        self.short_position = 0  # 空头仓位大小
        self.long_order = None   # 多头订单
        self.short_order = None  # 空头订单
        
    def next(self):
        # 获取当前日期（转换为pandas.Timestamp）
        current_date = pd.Timestamp(self.data.datetime.date())
        
        # 获取当前信号
        try:
            current_signal = self.signals.loc[current_date]
        except (KeyError, IndexError):
            current_signal = 0  # 无信号时保持中性
        
        # 计算当前可用资金
        available_cash = self.broker.getcash()
        current_price = self.data.close[0]
        
        # 计算基于波动率的仓位调整因子
        volatility_factor = 1.0 / (1 + self.volatility[0])
        
        # 多头逻辑
        if current_signal >= 0:  # 信号为正或中性时调整多头仓位
            # 计算目标多头价值（考虑权重和波动率）
            target_long_value = available_cash * self.p.long_weight * volatility_factor
            target_long_size = int(target_long_value / current_price)
            
            # 调整多头仓位
            if target_long_size > self.long_position:
                # 增加多头仓位
                self.long_order = self.buy(
                    size=target_long_size - self.long_position
                )
                self.long_position = target_long_size
                print(f"增加多头: {current_date.date()}, 价格={current_price:.2f}, 数量={target_long_size - self.long_position}")
            elif target_long_size < self.long_position:
                # 减少多头仓位
                self.long_order = self.sell(
                    size=self.long_position - target_long_size
                )
                self.long_position = target_long_size
                print(f"减少多头: {current_date.date()}, 价格={current_price:.2f}, 数量={self.long_position - target_long_size}")
        
        # 空头逻辑
        if current_signal <= 0:  # 信号为负或中性时调整空头仓位
            # 计算目标空头价值（考虑权重和波动率）
            target_short_value = available_cash * self.p.short_weight * volatility_factor
            target_short_size = int(target_short_value / current_price)
            
            # 调整空头仓位
            if target_short_size > self.short_position:
                # 增加空头仓位
                self.short_order = self.sell(
                    size=target_short_size - self.short_position
                )
                self.short_position = target_short_size
                print(f"增加空头: {current_date.date()}, 价格={current_price:.2f}, 数量={target_short_size - self.short_position}")
            elif target_short_size < self.short_position:
                # 减少空头仓位
                self.short_order = self.buy(
                    size=self.short_position - target_short_size
                )
                self.short_position = target_short_size
                print(f"减少空头: {current_date.date()}, 价格={current_price:.2f}, 数量={self.short_position - target_short_size}")
        
        # 记录每日持仓情况
        print(f"{current_date.date()} - 多头:{self.long_position}, 空头:{self.short_position}, 现金:{available_cash:.2f}")


# 回测主函数
def run_simple_backtest(data, d=30, slope_window=5, initial_cash=100000):
    cerebro = bt.Cerebro()
    cerebro.addstrategy(LongShortLLTStrategy, d=d, slope_window=slope_window)
    
    # 添加数据
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)
    
    # 设置初始资金和佣金
    cerebro.broker.setcash(initial_cash)
    cerebro.broker.setcommission(commission=0.001)  # 0.1%
    
    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    
    # 运行回测
    print(f"初始资金: {cerebro.broker.getvalue():.2f}")
    results = cerebro.run()
    print(f"最终资金: {cerebro.broker.getvalue():.2f}")
    
    # 输出绩效指标
    strat = results[0]
    print(f"年化收益率: {strat.analyzers.returns.get_analysis()['rnorm100']:.2f}%")
    print(f"夏普比率: {strat.analyzers.sharpe.get_analysis()['sharperatio']:.2f}")
    print(f"最大回撤: {strat.analyzers.drawdown.get_analysis()['max']['drawdown']:.2f}%")
    
    # 绘制结果
    # cerebro.plot(style='candlestick')
    
    return results

start = datetime.date(2005, 9, 6)
end = datetime.date(2013, 6, 28)

prices = get_price("000001.SH", start, end)
prices.index = pd.to_datetime(prices.index)

result = run_simple_backtest(prices)


start = datetime.date(2013, 6, 28)
end = datetime.date(2024,12, 31)

prices = get_price("000001.SH", start, end)
result = run_backtest(prices, alpha=0.03)


