import backtrader as bt
import pandas as pd
import numpy as np

# 您提供的LLT计算函数
def calculate_llt(prices, alpha=0.05):
    n = len(prices)
    llt = np.zeros(n)
    if n >= 1:
        llt[0] = prices[0]
    if n >= 2:
        llt[1] = prices[1]
    
    a1 = alpha - (alpha**2) / 4
    a2 = (alpha**2) / 2
    a3 = alpha - 3 * (alpha**2) / 4
    a4 = 2 * (1 - alpha)
    a5 = - (1 - alpha)**2
    
    for t in range(2, n):
        llt[t] = a1 * prices[t] + a2 * prices[t-1] - a3 * prices[t-2] + a4 * llt[t-1] + a5 * llt[t-2]
    
    return llt

# 您提供的信号计算函数
def llt_slope_signal(df, d: int=30, slope_window=5):
    df = df.copy()
    alpha = 2 / (d + 1)
    df["llt"] = calculate_llt(df["close"], alpha)
    df['slope'] = (df["llt"].rolling(slope_window)
                    .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))
    
    signals = pd.Series(0, index=df.index)
    signals[df['slope'] > 0] = 1
    signals[df['slope'] < 0] = -1
    
    return signals


class LongShort1x1Strategy(bt.Strategy):
    params = (
        ('d', 5),                # LLT参数d
        ('slope_window', 5),      # 斜率窗口
        ('position_ratio', 1),
    )
    
    def __init__(self):
        # 预计算信号
        self.signals = llt_slope_signal(
            self.data._dataname,  # 访问原始DataFrame
            d=5,
            slope_window=self.p.slope_window
        )
        self.signals = self.signals.tz_localize(None)  # 统一日期格式
        
        # 记录当前持仓方向（无需跟踪订单）
        self.position_direction = 0  # 0=无持仓，1=多头，-1=空头
    
    def next(self):
        # 获取当前日期和信号
        current_date = pd.Timestamp(self.data.datetime.date())
        
        # 获取当前信号（处理日期不匹配）
        try:
            current_signal = self.signals.loc[current_date]
        except KeyError:
            current_signal = 0
        
        current_price = self.data.close[0]
        portfolio_value = self.broker.getvalue()  # 总资产价值
        
        # 计算仓位大小（基于总资产的固定比例）
        target_value = portfolio_value * self.p.position_ratio
        target_size = max(1, int(target_value / current_price))  # 至少1股
        
        # 多空切换逻辑（回测专用，无需考虑未成交订单）
        if current_signal == 1:  # 信号为多头
            if self.position_direction == -1:
                # 当前有空头 → 平空 + 开多
                self.close()  # 平掉空头（回测中立即成交）
                self.buy(size=target_size)  # 开多（立即成交）
                self.position_direction = 1
                print(f"平空开多: {current_date.date()}, 价格={current_price:.2f}, 数量={target_size}")
            elif self.position_direction == 0:
                # 无持仓 → 直接开多
                self.buy(size=target_size)
                self.position_direction = 1
                print(f"开多: {current_date.date()}, 价格={current_price:.2f}, 数量={target_size}")
                
        elif current_signal == -1:  # 信号为空头
            if self.position_direction == 1:
                # 当前有多头 → 平多 + 开空
                self.close()  # 平掉多头（立即成交）
                self.sell(size=target_size)  # 开空（立即成交）
                self.position_direction = -1
                print(f"平多开空: {current_date.date()}, 价格={current_price:.2f}, 数量={target_size}")
            elif self.position_direction == 0:
                # 无持仓 → 直接开空
                self.sell(size=target_size)
                self.position_direction = -1
                print(f"开空: {current_date.date()}, 价格={current_price:.2f}, 数量={target_size}")
                
        else:  # 信号为0 → 平仓
            if self.position_direction != 0:
                self.close()  # 平仓（立即成交）
                self.position_direction = 0
                print(f"平仓: {current_date.date()}, 价格={current_price:.2f}")


# 回测执行函数（保持不变）
def run_backtest(data, d=30, slope_window=5, initial_cash=100000):
    # 数据清洗（关键：避免绘图错误）
    data = data.replace([np.inf, -np.inf], np.nan).dropna()
    if 'volume' in data.columns:
        data['volume'] = data['volume'].clip(lower=0)  # 成交量非负
    
    # 初始化回测引擎
    cerebro = bt.Cerebro()
    cerebro.addstrategy(LongShort1x1Strategy, d=d, slope_window=slope_window)
    
    # 添加数据
    bt_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(bt_data)
    
    # 配置回测参数
    cerebro.broker.setcash(initial_cash)
    cerebro.broker.setcommission(commission=0.001)  # 佣金0.1%
    
    # 添加绩效分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    
    # 运行回测
    print(f"初始资金: {cerebro.broker.getvalue():.2f}")
    results = cerebro.run()
    final_value = cerebro.broker.getvalue()
    print(f"最终资金: {final_value:.2f}")
    
    # 输出绩效指标（处理可能的None值）
    strat = results[0]
    returns = strat.analyzers.returns.get_analysis()
    sharpe = strat.analyzers.sharpe.get_analysis()
    drawdown = strat.analyzers.drawdown.get_analysis()
    
    print(f"年化收益率: {returns.get('rnorm100', 0):.2f}%")
    print(f"夏普比率: {sharpe.get('sharperatio', 0):.2f}")
    print(f"最大回撤: {drawdown.get('max', {}).get('drawdown', 0):.2f}%")


    return results

def get_price(symbol, start_date, end_date):
    pro = pro_api()

    price_df = pro.index_daily(
        ts_code=symbol,
        start_date=start_date.strftime("%Y%m%d"),
        end_date=end_date.strftime("%Y%m%d"),
    )

    price_df = (
        price_df.rename({"trade_date": "date", "ts_code": "asset"}, axis=1)
        .sort_values("date", ascending=True)
        .set_index("date")
    )

    return price_df

start = datetime.date(2013, 1, 1)
end = datetime.date(2024,12, 31)

prices = get_price("000001.SH", start, end)
prices.index = pd.to_datetime(prices.index)
result = run_backtest(prices, d = 5)

pd.options.display.max_rows = 300
result[0].signals.head(100)

