"""
使用 Backtrader 实现 LLT 策略回测
"""

import datetime

import backtrader as bt
import numpy as np
import pandas as pd


class SlopeIndicator(bt.Indicator):
    """斜率指标 - 计算指定周期内的线性回归斜率"""
    lines = ('slope',)
    params = (('period', 5),)
    
    def __init__(self):
        self.addminperiod(self.p.period)
        
    def next(self):
        # 获取最近period个数据点
        y_values = [self.data[i] for i in range(-self.p.period + 1, 1)]
        x_values = list(range(self.p.period))
        
        # 计算线性回归斜率
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        
        # 斜率公式: slope = (n*sum_xy - sum_x*sum_y) / (n*sum_x2 - sum_x*sum_x)
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator != 0:
            slope = (n * sum_xy - sum_x * sum_y) / denominator
        else:
            slope = 0
            
        self.l.slope[0] = slope


class LLTIndicator(bt.Indicator):
    """LLT (Low Lag Trend) 指标"""
    lines = ('llt',)
    params = (('alpha', 0.05),)
    
    def __init__(self):
        self.addminperiod(3)  # 至少需要3个数据点
        
    def next(self):
        if len(self.data) < 3:
            self.l.llt[0] = self.data.close[0]
            return
            
        # LLT计算系数
        alpha = self.p.alpha
        a1 = alpha - (alpha**2) / 4
        a2 = (alpha**2) / 2
        a3 = alpha - 3 * (alpha**2) / 4
        a4 = 2 * (1 - alpha)
        a5 = -((1 - alpha) ** 2)
        
        # 递归计算LLT
        if len(self.l.llt) >= 2:
            self.l.llt[0] = (
                a1 * self.data.close[0] +
                a2 * self.data.close[-1] -
                a3 * self.data.close[-2] +
                a4 * self.l.llt[-1] +
                a5 * self.l.llt[-2]
            )
        else:
            self.l.llt[0] = self.data.close[0]


class LLTStrategy(bt.Strategy):
    """基于LLT斜率的交易策略"""
    params = (
        ('alpha', 0.05),
        ('slope_window', 5),
        ('long_weight', 0.5),
        ('short_weight', 0.5),
    )
    
    def __init__(self):
        # 添加LLT指标
        self.llt = LLTIndicator(alpha=self.p.alpha)
        
        # 计算LLT斜率
        self.slope = SlopeIndicator(self.llt.llt, period=self.p.slope_window)
        
        # 生成信号
        self.signal = bt.If(self.slope > 0, 1, bt.If(self.slope < 0, -1, 0))
        
        # 记录变量
        self.order = None
        self.position_size = 0
        self.trade_count = 0
        
    def next(self):
        if self.order:
            return
            
        current_signal = self.signal[0]
        
        # 多头信号
        if current_signal == 1 and self.position_size <= 0:
            if self.position_size < 0:
                # 平空仓
                self.order = self.close()
            else:
                # 开多仓
                size = self.broker.get_cash() * self.p.long_weight / self.data.close[0]
                self.order = self.buy(size=size)
                self.position_size = 1
                
        # 空头信号  
        elif current_signal == -1 and self.position_size >= 0:
            if self.position_size > 0:
                # 平多仓
                self.order = self.close()
            else:
                # 开空仓
                size = self.broker.get_cash() * self.p.short_weight / self.data.close[0]
                self.order = self.sell(size=size)
                self.position_size = -1
                
    def notify_order(self, order):
        if order.status in [order.Completed]:
            self.order = None
            
    def notify_trade(self, trade):
        if trade.isclosed:
            self.position_size = 0
            self.trade_count += 1


def run_backtrader_llt(data_df, alpha=0.05, slope_window=5, initial_cash=100000):
    """运行backtrader LLT策略回测"""
    
    # 准备数据
    data_df = data_df.copy()
    data_df.index = pd.to_datetime(data_df.index)
    
    # 创建backtrader数据源
    data = bt.feeds.PandasData(
        dataname=data_df,
        datetime=None,
        open='open',
        high='high', 
        low='low',
        close='close',
        volume='vol' if 'vol' in data_df.columns else None,
        openinterest=None
    )
    
    # 创建cerebro引擎
    cerebro = bt.Cerebro()
    
    # 添加策略
    cerebro.addstrategy(LLTStrategy, alpha=alpha, slope_window=slope_window)
    
    # 添加数据
    cerebro.adddata(data)
    
    # 设置初始资金
    cerebro.broker.setcash(initial_cash)
    
    # 设置手续费
    cerebro.broker.setcommission(commission=0.001)
    
    # 运行回测
    print(f'初始资金: {cerebro.broker.getvalue():.2f}')
    result = cerebro.run()
    final_value = cerebro.broker.getvalue()
    print(f'最终资金: {final_value:.2f}')
    
    # 计算收益率
    total_return = (final_value - initial_cash) / initial_cash
    print(f'总收益率: {total_return:.2%}')
    
    # 计算基准收益率
    benchmark_return = (data_df['close'].iloc[-1] - data_df['close'].iloc[0]) / data_df['close'].iloc[0]
    print(f'基准收益率: {benchmark_return:.2%}')
    print(f'超额收益: {total_return - benchmark_return:.2%}')
    
    # 获取策略实例
    strategy = result[0]
    print(f'交易次数: {strategy.trade_count}')
    
    return {
        'initial_cash': initial_cash,
        'final_value': final_value,
        'total_return': total_return,
        'benchmark_return': benchmark_return,
        'excess_return': total_return - benchmark_return,
        'trade_count': strategy.trade_count,
        'strategy': strategy
    }


# 简化版本 - 只做多策略
class LLTLongOnlyStrategy(bt.Strategy):
    """只做多的LLT策略"""
    params = (
        ('alpha', 0.05),
        ('slope_window', 5),
    )

    def __init__(self):
        self.llt = LLTIndicator(alpha=self.p.alpha)
        self.slope = SlopeIndicator(self.llt.llt, period=self.p.slope_window)
        self.order = None

    def next(self):
        if self.order:
            return

        # 只做多头交易
        if self.slope[0] > 0 and not self.position:
            # 买入
            self.order = self.buy()
        elif self.slope[0] < 0 and self.position:
            # 卖出
            self.order = self.sell()

    def notify_order(self, order):
        if order.status in [order.Completed]:
            self.order = None


def run_simple_llt(data_df, alpha=0.05, slope_window=5, initial_cash=100000):
    """运行简化版LLT策略"""

    data_df = data_df.copy()
    data_df.index = pd.to_datetime(data_df.index)

    data = bt.feeds.PandasData(
        dataname=data_df,
        datetime=None,
        open='open',
        high='high',
        low='low',
        close='close',
        volume=None,
        openinterest=None
    )

    cerebro = bt.Cerebro()
    cerebro.addstrategy(LLTLongOnlyStrategy, alpha=alpha, slope_window=slope_window)
    cerebro.adddata(data)
    cerebro.broker.setcash(initial_cash)
    cerebro.broker.setcommission(commission=0.001)

    print(f'初始资金: {cerebro.broker.getvalue():.2f}')
    cerebro.run()
    final_value = cerebro.broker.getvalue()
    print(f'最终资金: {final_value:.2f}')

    total_return = (final_value - initial_cash) / initial_cash
    benchmark_return = (data_df['close'].iloc[-1] - data_df['close'].iloc[0]) / data_df['close'].iloc[0]

    print(f'策略收益率: {total_return:.2%}')
    print(f'基准收益率: {benchmark_return:.2%}')
    print(f'超额收益: {total_return - benchmark_return:.2%}')

    return total_return, benchmark_return


# 使用示例
if __name__ == "__main__":
    # 示例：使用您的price_df数据
    # result = run_backtrader_llt(price_df, alpha=0.05, slope_window=5)
    # simple_result = run_simple_llt(price_df, alpha=0.05, slope_window=5)

    print("请在notebook中导入此文件并使用:")
    print("from llt_backtrader import run_backtrader_llt, run_simple_llt")
    print("result = run_backtrader_llt(price_df)")
    print("simple_result = run_simple_llt(price_df)")
