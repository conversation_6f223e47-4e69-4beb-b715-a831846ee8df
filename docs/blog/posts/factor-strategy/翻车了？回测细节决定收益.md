在昨天的《『译』研报：低延迟趋势线与交易择时》文章里，LLT均线切线买入策略的回测结果好到难以置信。这个收益究竟有几分可信度呢？

借这个机会，讨论一下策略回测中的几个关键问题。

## 未来数据

在我们昨天的研报复现中，实际上是有一点使用未来数据的嫌疑的。问题出在下面的代码中：

```python
df = df.copy()
df['slope'] = (df[factor_col].rolling(slope_window)
                .apply(lambda x: np.polyfit(np.arange(slope_window), x, 1)[0]))

df['signal'] = 0
df.loc[df['slope'] > 0, 'signal'] = 1
df.loc[df['slope'] < 0, 'signal'] = -1

# 计算每日收益率
df['benchmark'] = df['close'].pct_change()

# 计算多空组合收益
df['long_return'] = np.where(df['signal'] == 1, df['benchmark'], 0)
df['short_return'] = np.where(df['signal'] == -1, -df['benchmark'], 0)

# 组合收益 = 多头收益 * 多头权重 + 空头收益 * 空头权重
df['strategy'] = df['long_return'] * long_weight + df['short_return'] * short_weight

return df
```

在这里，我们是取signal为1时的当天收益作为多头收益。但是，当日收益的确切含义是，你要在昨天买入，今天才能计算收益。

如果今天股价上涨，那么均线切线就可能方向向上，从而signal = 1；如果今天股价下跌，均线切线就有可能方向向下，从而signal = -1。这两种情况都被不合理的计入了收益。

按照研报，它是这样建仓的：

![](https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250717143703.png)

这句话虽然没有包含实现细节，但本质上也是包含了未来数据的，不过它的副作用会小一点：它只是要求在收盘后，根据收盘价计算出来趋势线切线斜率和信号后，再以收盘价买入。

尽管这也有一点点未来数据的含义，但在实践中是允许的，因为理论上你可以赶在尾盘集合竞价时计算信号并买入：这样你计算信号时使用的价格，与最终的收盘价相差并不大，很可能就是一个滑点的价格。在一些回测工具中，也是允许这样做的。比如，backtrader就允许以当天收盘价买入，只要你声明允许COC(Cheat on Close)。

## 修正算法

一般来说，修正


![](https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/vector-backtest.jpeg)
