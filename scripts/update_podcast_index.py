#!/usr/bin/env python
import os
import glob
from datetime import datetime
from pathlib import Path
import frontmatter

def extract_podcast_info():
    """从播客文件中提取信息"""
    podcasts = []
    podcast_files = glob.glob("docs/podcast/*.md")
    
    for file in podcast_files:
        if file.endswith("index.md"):
            continue
            
        with open(file, "r", encoding="utf-8") as f:
            post = frontmatter.load(f)
            
        if "title" in post.metadata and "date" in post.metadata:
            # 构建URL
            url = "/podcast/" + os.path.basename(file).replace(".md", "/")
            
            podcasts.append({
                "title": post.metadata.get("title"),
                "url": url,
                "date": post.metadata.get("date"),
                "description": post.metadata.get("description", ""),
                "audio": post.metadata.get("audio", "")
            })
    
    # 按日期排序
    podcasts.sort(key=lambda x: datetime.strptime(str(x["date"]), "%Y-%m-%d"), reverse=True)
    return podcasts

def generate_index_page(podcasts):
    """生成播客索引页面"""
    template = """---
title: 量化好声音播客
---

# 量化好声音播客

欢迎收听"量化好声音"播客！在这里，我们探讨量化交易的各种话题，从因子研究到策略实现，从市场洞察到技术分享。

## 最新播客

<div class="podcast-grid">
{podcast_cards}
</div>

<style>
.podcast-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.podcast-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.podcast-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.podcast-info {
  padding: 15px;
}

.podcast-date {
  color: #666;
  font-size: 0.9em;
  margin: 5px 0;
}

.podcast-desc {
  font-size: 0.95em;
  color: #333;
  margin-top: 10px;
}
</style>
"""
    
    podcast_cards = ""
    for podcast in podcasts:
        podcast_cards += f"""  <div class="podcast-card">
    <a href="{podcast['url']}">
      <div class="podcast-info">
        <h3>{podcast['title']}</h3>
        <p class="podcast-date">{podcast['date']}</p>
        <p class="podcast-desc">{podcast['description']}</p>
      </div>
    </a>
  </div>
"""
    
    index_content = template.format(podcast_cards=podcast_cards)
    
    with open("docs/podcast/index.md", "w", encoding="utf-8") as f:
        f.write(index_content)
    
    print(f"Generated index page with {len(podcasts)} podcasts")

if __name__ == "__main__":
    podcasts = extract_podcast_info()
    generate_index_page(podcasts)