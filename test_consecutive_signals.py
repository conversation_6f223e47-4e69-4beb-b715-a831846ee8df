#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连续信号过滤功能
"""

import pandas as pd
import numpy as np
from backtest_reporter import create_backtest_report

def create_test_data_with_consecutive_signals():
    """创建包含连续信号的测试数据"""
    dates = pd.date_range('2023-01-01', periods=50, freq='D')
    
    # 创建价格数据
    np.random.seed(42)
    prices = 100 + np.cumsum(np.random.randn(50) * 0.5)
    
    df = pd.DataFrame({
        'open': prices,
        'high': prices * 1.02,
        'low': prices * 0.98,
        'close': prices,
        'volume': np.random.randint(1000, 10000, 50)
    }, index=dates)
    
    return df

def consecutive_signal_strategy(df):
    """生成包含连续信号的策略"""
    signals = pd.Series(0, index=df.index)
    
    # 手动设置一些连续的买入和卖出信号
    # 连续买入信号：第5-8天
    signals.iloc[5:9] = 1
    
    # 连续卖出信号：第15-18天
    signals.iloc[15:19] = -1
    
    # 单独买入信号：第25天
    signals.iloc[25] = 1
    
    # 连续买入信号：第30-32天
    signals.iloc[30:33] = 1
    
    # 连续卖出信号：第40-43天
    signals.iloc[40:44] = -1
    
    return signals

def test_consecutive_signals():
    """测试连续信号过滤功能"""
    print("=== 测试连续信号过滤功能 ===")
    
    # 创建测试数据
    data = create_test_data_with_consecutive_signals()
    
    # 生成包含连续信号的策略
    signals = consecutive_signal_strategy(data)
    
    print("\n原始信号序列：")
    signal_summary = []
    for i, signal in enumerate(signals):
        if signal != 0:
            signal_type = "买入" if signal == 1 else "卖出"
            signal_summary.append(f"第{i+1}天: {signal_type}")
    
    for item in signal_summary:
        print(item)
    
    print(f"\n原始信号统计：")
    print(f"买入信号总数: {(signals == 1).sum()}")
    print(f"卖出信号总数: {(signals == -1).sum()}")
    
    # 手动测试过滤函数
    def filter_consecutive_signals(signal_series, target_value):
        """过滤连续信号，只保留每组连续信号的第一个"""
        filtered_indices = []
        prev_signal = 0
        
        for i, signal in enumerate(signal_series):
            if signal == target_value and prev_signal != target_value:
                filtered_indices.append(i)
            prev_signal = signal
        
        return filtered_indices
    
    buy_indices = filter_consecutive_signals(signals, 1)
    sell_indices = filter_consecutive_signals(signals, -1)
    
    print(f"\n过滤后的信号：")
    print(f"买入信号位置: {[i+1 for i in buy_indices]}")
    print(f"卖出信号位置: {[i+1 for i in sell_indices]}")
    print(f"过滤后买入信号数: {len(buy_indices)}")
    print(f"过滤后卖出信号数: {len(sell_indices)}")
    
    # 创建回测报告
    print("\n生成回测报告...")
    fig = create_backtest_report(
        data,
        lambda df: consecutive_signal_strategy(df),
        (),
        long_short=True,
        title="连续信号过滤测试"
    )
    
    print("\n测试完成！请查看生成的图表，确认：")
    print("1. 第5-8天的连续买入信号只显示第5天的标记")
    print("2. 第15-18天的连续卖出信号只显示第15天的标记")
    print("3. 第25天的单独买入信号正常显示")
    print("4. 第30-32天的连续买入信号只显示第30天的标记")
    print("5. 第40-43天的连续卖出信号只显示第40天的标记")
    
    return fig

if __name__ == "__main__":
    fig = test_consecutive_signals()
    fig.show()