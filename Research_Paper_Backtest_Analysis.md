# 东吴证券UBL因子研报回测参数分析

## 研报基本信息

**研报标题**：《上下影线，蜡烛好还是威廉好？》  
**发布机构**：东吴证券  
**主要作者**：高子剑等  
**发布时间**：2020年6月  

## 从代码和文档中推断的研报回测参数

### 1. 回测时间段

根据多个文档中的信息：

**主要回测期间：2009年1月 - 2020年4月**
- 在`ml-predict-peaks-valleys-1.md`中明确提到："在2009到2020年4月，以全A为样本"
- 在`因子分析4.md`中显示测试代码使用：`start = datetime.date(2009, 1, 1)` 到 `end = datetime.date(2023, 1, 1)`

**总回测时长**：约11年3个月

### 2. 股票池范围

**全A股市场**
- 文档中明确提到"以全A为样本"
- 这意味着包含了当时A股市场上所有可交易的股票
- 估计股票数量：2009年约1600只，2020年约4000只

**股票池特征**：
- 包含主板、中小板、创业板
- 可能排除了ST股票和停牌股票
- 动态调整，新股上市后纳入，退市股票剔除

### 3. 因子构建参数

**滚动窗口期**：20个交易日
- 代码中默认使用`lookback_period=20`
- 这是一个月度的滚动窗口

**因子组成**：
- 上影线比率（负向因子）
- 下影线比率（正向因子）  
- 威廉指标上部分（负向因子）
- 威廉指标下部分（正向因子）

### 4. 回测方法

**分层测试**：5组分层多空测试
- 将股票按UBL因子值分为5个分位数
- 做多最高分位数，做空最低分位数
- 构建多空组合进行回测

**调仓频率**：推测为月度调仓
- 基于因子计算的滚动窗口特性
- 符合机构投资者的操作习惯

### 5. 研报核心结论

**回测表现（2009-2020年4月）**：
- **年化收益率**：15.86%
- **最大回撤**：3.68%
- **信息比率**：较高（具体数值未在文档中找到）

**因子有效性**：
- 具有非常明显的信号意义
- 多空组合表现稳定
- 风险调整后收益优秀

## 当前实现与研报的对比

### 时间段对比

| 项目 | 研报原始 | 当前实现 |
|------|----------|----------|
| 开始时间 | 2009年1月 | 2020年1月 |
| 结束时间 | 2020年4月 | 2023年12月 |
| 回测时长 | 11年3个月 | 4年 |

### 股票池对比

| 项目 | 研报原始 | 当前实现 |
|------|----------|----------|
| 范围 | 全A股 | 50只股票（测试用） |
| 数量 | 1600-4000只 | 50只 |
| 覆盖度 | 100% | <2% |

### 参数对比

| 项目 | 研报原始 | 当前实现 |
|------|----------|----------|
| 滚动窗口 | 20日 | 20日 ✓ |
| 因子构建 | 正确 | 正确 ✓ |
| 分层数量 | 5组 | 5组 ✓ |

## 建议的改进方案

### 1. 复现研报原始设置

```python
def replicate_research_paper():
    # 使用研报原始参数
    start_date = datetime.date(2009, 1, 1)
    end_date = datetime.date(2020, 4, 30)
    universe_size = -1  # 全A股
    
    # 运行分析
    factor_data, prices = prepare_factor_data(start_date, end_date, universe_size)
    clean_factor_data = run_alphalens_analysis(factor_data, prices)
```

### 2. 扩展到最新数据

```python
def extended_analysis():
    # 扩展到最新数据
    start_date = datetime.date(2009, 1, 1)
    end_date = datetime.date(2024, 12, 31)  # 最新数据
    universe_size = -1  # 全A股
    
    # 分段分析
    periods = [
        (datetime.date(2009, 1, 1), datetime.date(2020, 4, 30)),  # 原研报期间
        (datetime.date(2020, 5, 1), datetime.date(2024, 12, 31))  # 样本外测试
    ]
```

### 3. 数据质量控制

```python
def data_quality_control():
    # 排除条件
    exclude_conditions = [
        "ST股票",
        "停牌股票", 
        "上市不足60天的新股",
        "日成交额低于1000万的股票"
    ]
```

## 研报价值与意义

### 1. 理论贡献
- 将主观技术分析量化化
- 证明了传统技术指标的有效性
- 提供了因子构建的新思路

### 2. 实践价值
- 年化15.86%的收益率具有很强的实用性
- 3.68%的最大回撤显示了良好的风险控制
- 可以作为多因子模型的组成部分

### 3. 方法论启示
- 简单指标通过巧妙组合可以产生显著效果
- 量化方法可以验证和改进传统经验
- 长期回测的重要性

## 总结

东吴证券的这份研报使用了**2009年1月至2020年4月**的**全A股数据**进行回测，时间跨度超过11年，覆盖了完整的市场周期。研报的核心价值在于：

1. **时间跨度充分**：11年的回测期间包含了多个牛熊周期
2. **样本覆盖全面**：全A股样本确保了结论的普适性  
3. **方法科学严谨**：5组分层测试是标准的因子检验方法
4. **结果显著有效**：15.86%年化收益和3.68%最大回撤的组合非常优秀

当前实现在算法逻辑上是正确的，但在回测规模和时间跨度上与原研报存在较大差距。建议按照研报原始参数进行完整复现，以验证因子在更大样本和更长时间上的有效性。
