
# ========== 终极中文字体解决方案 ==========
# 直接复制此代码到 Notebook cell 中运行

import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.font_manager import FontProperties
import numpy as np
import os

# 直接指定字体文件路径
font_path = '/Users/<USER>/Library/Fonts/WenQuanWeiMiHei.ttf'

if os.path.exists(font_path):
    # 方法1: 添加字体到 matplotlib
    mpl.font_manager.fontManager.addfont(font_path)
    font_name = FontProperties(fname=font_path).get_name()
    plt.rcParams['font.sans-serif'] = [font_name]
    print(f"✅ 使用字体: {font_name}")
else:
    # 备用方案
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
    print("✅ 使用备用字体")

plt.rcParams['axes.unicode_minus'] = False

# 创建字体属性对象（最可靠的方法）
chinese_font = FontProperties(fname=font_path) if os.path.exists(font_path) else None

# 测试函数
def plot_with_chinese():
    fig, ax = plt.subplots(figsize=(10, 6))
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    ax.plot(x, y, 'b-', linewidth=2, label='正弦波')
    
    # 使用 rcParams 设置的字体
    ax.set_title('低延迟趋势线与交易择时', fontsize=16)
    ax.set_xlabel('时间', fontsize=12)
    ax.set_ylabel('价格', fontsize=12)
    
    # 使用直接字体属性（更可靠）
    if chinese_font:
        ax.text(5, 0.5, '这是直接指定字体的中文文本', 
                fontproperties=chinese_font, fontsize=12,
                bbox=dict(boxstyle="round", facecolor="lightblue"))
    
    ax.legend()
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    # 验证
    print(f"实际使用字体: {ax.title.get_fontname()}")

# 运行测试
plot_with_chinese()
