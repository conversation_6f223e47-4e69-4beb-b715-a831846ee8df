<link href="assets/css/bootstrap.min.4.0.css" rel="stylesheet" />
<link href="assets/css/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet" />
<meta name="viewport" content="width=device-width, initial-scale=1">


<style>
  .md-typeset h1,
  .md-content__button {
    display: none;
  }

.md-typeset hr {
    display: none;
}

.as-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
}



@media (min-width: 768px) { 
    .card-columns {
        column-count: 2;
    }
 }

@media (min-width: 1200px) { 
    .card-columns {
        column-count: 3;
    }

    .md-sidebar--primary {
    display: none;
    }
 }

a .card-title {
    color: rgb(55, 58, 60);
    font-size: 17px;
}

a .card-text {
    color: rgb(55, 58, 60);
    font-size: 14px;
}

a:hover {
    color: inherit;
    text-decoration: inherit;
}

nav a {
    font-size: 0.8rem !important;
    color: white;
    mix-blend-mode: difference;
}
</style>

<div class="as-grid m-t-md">
<div class="card-columns">
    
<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/factor-strategy/低延迟趋势线与交易择时/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/neom-tSwRu3Jh0EM-unsplash.jpg"/>
    <div class="card-body">
        <h4 class="card-title">Z变换改造均线，一个12年前的策略为何仍能跑赢大盘？</h4>
        <p class="card-text">传统移动平均线（MA）是技术分析中常用的趋势跟踪指标，通过对股票价格或指数在一定天数内的平均值进行计算，以刻画其变动方向。MA 的计算天数越多，其平滑性越好，但随之而来的时滞（延迟）影响也越严重。这意味着 MA 指标在跟踪趋势时容易出现“跟不紧”甚至“跟不上”的情况，平滑性...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-16</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/python/it-is-π-thon/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/hot/meme/π-thon.png"/>
    <div class="card-body">
        <h4 class="card-title">π-thon以及他的朋友们</h4>
        <p class="card-text">最近的Python社区热闹异常。在6月中旬，Python发布了Python 3.14 beta3。它可不是一个普通的预发布版本 -- 它是第一个正式支持期待已久的自由线程或『无 GIL』的版本。而有没有GIL，绝对是Python发展史上的有一个分水岭。这个版本将在今年的程序...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-15</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/papers/ubl-2/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/hot/mybook/book-with-course.png"/>
    <div class="card-body">
        <h4 class="card-title">『匡醍译研报 02』 驯龙高手，从股谚到量化因子的工程化落地</h4>
        <p class="card-text">上一期文章中，我们复现了研报的因子构建部分，分别是影线因子、威廉影线因子以及由此组合而来的 UBL 因子。这一期我们将对这些因子进行检验。<br><br>因子检验固然是因子挖掘中必不可少的一环，但它应该是一个 routine 的工作 -- 我们不应该每次都重新发明轮子。然而...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-04</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/papers/ubl/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/06/dragons.png"/>
    <div class="card-body">
        <h4 class="card-title">『匡醍译研报 01』 驯龙高手，从股谚到量化因子的工程化落地</h4>
        <p class="card-text">头上三柱香，不死也赔光。这是一句股谚，说得是如果在高位出现三根长上影线，那么股价短期内很可能会下跌。因为上影线代表了上面的抛压特别大。这种说法能得到统计数据上的验证吗？来自东吴证券的一份研报，就讨论了这个问题。<br><br>这份研报给出一个很价值的结论，那就是，影线好不好...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-29</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/factor-strategy/构建强化学习交易模型/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/06/20250625204449.png"/>
    <div class="card-body">
        <h4 class="card-title">强化学习模型能否自我演化出交易智慧？</h4>
        <p class="card-text">!!! abstract 内容摘要<br>    * 强化学习已在摩根大通及全球顶级投资机构中使用。<br>    * 与监督学习不同，强化学习不会在每一步都只接受标准答案，它会尝试、忍受短期的损失，博取长期的收益。这就使得它有了对抗金融数据噪声的能力。<br>    * ...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-25</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/tools/quantstats-reloaded/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/university/Mackey_Auditorium-Colorado.jpg"/>
    <div class="card-body">
        <h4 class="card-title">Quantstats Reloaded</h4>
        <p class="card-text">Quantstats 是一款用于交易策略绩效分析的 Python 库，深受量化圈用户喜爱，在 Github 上获得了超过 5.8k 的 stars。但很遗憾，由于原作者长期未维护，现在新安装的 Quantstats，尤其是在 Python 3.12 及以上高版本中，几乎无法...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-16</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/tools/21天驯化AI打工仔/8_QMT实时分钟线数据订阅系统/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/hot/mybook/swimsuit.jpg"/>
    <div class="card-body">
        <h4 class="card-title">21 天驯化 AI 打工仔: QMT 实时分笔数据订阅系统与多 Client 问题</h4>
        <p class="card-text">> 当数据如潮水般涌来，如何让系统稳如磐石？本文带你深入 QMT 实时数据订阅的世界，见证 007 助手如何将一个简单的数据获取程序，升级为处理能力提升 10 倍的高性能系统！<br><br>"007，我们的日线数据定时获取系统已经很稳定了，但现在我需要更细粒度的数据——分...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-15</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/tools/21天驯化AI打工仔/9_系统逻辑优化与分钟线数据合成/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/university/university-college-london-library.jpg"/>
    <div class="card-body">
        <h4 class="card-title">21 天驯化 AI 打工仔:系统逻辑优化与分钟线数据合成</h4>
        <p class="card-text">> 当分笔数据如潮水般涌来，如何让系统智能地将它们合成为有价值的分钟线数据？本文带你深入量化交易系统的核心——数据合成与系统架构优化的世界！<br><br>"007，我们的实时分笔数据订阅系统已经基本完成，但现在我遇到了一个新的挑战。"我一边查看着 Redis 中堆积如山的...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-15</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/papers/研报该如何复现/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/2025/05/20250514202750.png"/>
    <div class="card-body">
        <h4 class="card-title">把研报『翻译』成代码，80%的工作都在这篇文章里讲了</h4>
        <p class="card-text">如何读懂并复现研？这是我们学员提出来的一个问题。读懂并复现一篇研报，在理解研报的核心思想之外，看懂高频常用术语（行话、俚语）、实现概念到代码的转换、懂得如何获得数据是占80%的常规工作。在上一篇《RSRS择时指标》中，我们的重点在于复现策略本身。这一篇文章，我们重点介绍如何...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-11</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/tools/AI-tools/remote-agent/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/2025/05/remote-poster.png"/>
    <div class="card-body">
        <h4 class="card-title">Augment Remote Agent: 有了本地Agent，为什么你还需要Remote Agent?</h4>
        <p class="card-text">6 月 7 日，当我启动 Augment 准备继续编写策略时，弹出一条消息提示，大致是，我们刚刚发布了 Remote Agent，你要不要试一试？这个试用需要登录 github 账号，所以我的第一直觉就是，关掉它，我不需要它。<br><br><div style='widt...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-10</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/papers/rsrs择时指标/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/2025/05/20250514202750.png"/>
    <div class="card-body">
        <h4 class="card-title">RSRS 择时指标</h4>
        <p class="card-text">RSRS 因子在 2005 年 3 月到 2017 年 3 月的上证 50 指数上，12 年总收益 **1432.36%**，年化 **24.84%**，夏普 1.42。同期指数收益仅为 290.13%。该指标的大致思想是，将每日最高价与最低价分别视为阻力位与支撑位，把给定...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-09</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/algo/monte-carlo/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/hot/course/factor-ml/fa-platinum.png"/>
    <div class="card-body">
        <h4 class="card-title">蒙特卡洛：看似很高端的技术，其实很暴力很初级</h4>
        <p class="card-text">我们常常想知道投资组合在未来的某一天，最大损失会是多少，估算方法之一就是蒙特卡洛。尽管它在计算性能上不占优势，却最让人心里踏实 -- 毕竟，**它是一种把几乎所有的路径都走了一遍，再回来告诉你一路上的风险与风景的方法**。她看起来很高端，实际上只是很暴力。今天就带你认识她。</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-05</small></p>
    </div>
    </a>
</div><!--end-card-->

</div>
</div>


