#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极中文字体解决方案 - 直接指定字体文件路径
不依赖系统字体配置，直接使用字体文件
"""

import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.font_manager import FontProperties
import numpy as np
import os

def get_wenquanyi_font_path():
    """获取 WenQuanYi 字体文件的绝对路径"""
    possible_paths = [
        '/Users/<USER>/Library/Fonts/WenQuanWeiMiHei.ttf',
        '/Users/<USER>/Library/Fonts/wqy-microhei-lite.ttc',
        '/System/Library/Fonts/PingFang.ttc',
        '/System/Library/Fonts/Helvetica.ttc'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"找到字体文件: {path}")
            return path
    
    print("未找到合适的中文字体文件")
    return None

def create_chinese_font_property():
    """创建中文字体属性对象"""
    font_path = get_wenquanyi_font_path()
    if font_path:
        return FontProperties(fname=font_path)
    else:
        # 备用方案：使用系统默认中文字体
        return FontProperties(family=['Arial Unicode MS', 'SimHei'])

def setup_matplotlib_chinese():
    """设置 matplotlib 中文显示 - 终极方案"""
    print("=== 终极中文字体设置 ===")
    
    # 方案1: 直接指定字体文件
    font_path = get_wenquanyi_font_path()
    if font_path:
        # 使用绝对路径设置字体
        mpl.font_manager.fontManager.addfont(font_path)
        font_name = mpl.font_manager.FontProperties(fname=font_path).get_name()
        print(f"添加字体: {font_name}")
        
        # 设置为默认字体
        plt.rcParams['font.sans-serif'] = [font_name, 'Arial Unicode MS', 'SimHei']
    else:
        # 备用方案
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
    
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 12
    
    print("✅ 字体设置完成")
    print("当前字体列表:", plt.rcParams['font.sans-serif'][:3])

def test_chinese_ultimate():
    """终极中文显示测试"""
    print("\n=== 终极中文显示测试 ===")
    
    # 创建字体属性对象
    chinese_font = create_chinese_font_property()
    
    # 创建测试图形
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # 绘制图形
    ax.plot(x, y1, 'b-', label='正弦波', linewidth=2)
    ax.plot(x, y2, 'r--', label='余弦波', linewidth=2)
    
    # 方法1: 使用 rcParams 设置的字体
    ax.set_title('低延迟趋势线与交易择时分析', fontsize=16, pad=20)
    ax.set_xlabel('时间 (秒)', fontsize=12)
    ax.set_ylabel('价格变化', fontsize=12)
    
    # 方法2: 直接指定字体属性（更可靠）
    if chinese_font:
        ax.text(2, 0.8, '使用直接字体路径的中文文本', 
                fontproperties=chinese_font, fontsize=14,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # 添加图例和网格
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加更多中文元素
    ax.annotate('峰值点', xy=(np.pi/2, 1), xytext=(2, 1.2),
                arrowprops=dict(arrowstyle='->', color='green'),
                fontsize=12, color='green')
    
    plt.tight_layout()
    plt.show()
    
    # 验证字体
    title_font = ax.title.get_fontname()
    print(f"标题实际字体: {title_font}")
    
    return fig

def generate_notebook_code():
    """生成在 Notebook 中直接使用的代码"""
    code = '''
# ========== 终极中文字体解决方案 ==========
# 直接复制此代码到 Notebook cell 中运行

import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.font_manager import FontProperties
import numpy as np
import os

# 直接指定字体文件路径
font_path = '/Users/<USER>/Library/Fonts/WenQuanWeiMiHei.ttf'

if os.path.exists(font_path):
    # 方法1: 添加字体到 matplotlib
    mpl.font_manager.fontManager.addfont(font_path)
    font_name = FontProperties(fname=font_path).get_name()
    plt.rcParams['font.sans-serif'] = [font_name]
    print(f"✅ 使用字体: {font_name}")
else:
    # 备用方案
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
    print("✅ 使用备用字体")

plt.rcParams['axes.unicode_minus'] = False

# 创建字体属性对象（最可靠的方法）
chinese_font = FontProperties(fname=font_path) if os.path.exists(font_path) else None

# 测试函数
def plot_with_chinese():
    fig, ax = plt.subplots(figsize=(10, 6))
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    ax.plot(x, y, 'b-', linewidth=2, label='正弦波')
    
    # 使用 rcParams 设置的字体
    ax.set_title('低延迟趋势线与交易择时', fontsize=16)
    ax.set_xlabel('时间', fontsize=12)
    ax.set_ylabel('价格', fontsize=12)
    
    # 使用直接字体属性（更可靠）
    if chinese_font:
        ax.text(5, 0.5, '这是直接指定字体的中文文本', 
                fontproperties=chinese_font, fontsize=12,
                bbox=dict(boxstyle="round", facecolor="lightblue"))
    
    ax.legend()
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    # 验证
    print(f"实际使用字体: {ax.title.get_fontname()}")

# 运行测试
plot_with_chinese()
'''
    return code

def main():
    """主函数"""
    print("matplotlib 中文字体终极解决方案")
    print("=" * 50)
    
    # 设置字体
    setup_matplotlib_chinese()
    
    # 测试显示
    test_chinese_ultimate()
    
    # 生成 Notebook 代码
    notebook_code = generate_notebook_code()
    
    # 保存代码
    with open('notebook_ultimate_chinese.txt', 'w', encoding='utf-8') as f:
        f.write(notebook_code)
    
    print("\n" + "=" * 50)
    print("🎯 终极解决方案:")
    print("1. ✅ 直接使用字体文件路径")
    print("2. ✅ 创建字体属性对象")
    print("3. ✅ 提供多重备用方案")
    print("4. ✅ 生成 Notebook 专用代码")
    print()
    print("📁 Notebook 代码已保存到: notebook_ultimate_chinese.txt")
    print()
    print("🔧 使用方法:")
    print("1. 复制 notebook_ultimate_chinese.txt 中的代码")
    print("2. 粘贴到您的 Notebook 第一个 cell")
    print("3. 运行该 cell")
    print("4. 这种方法不依赖系统配置，应该能稳定工作")

if __name__ == "__main__":
    main()
