#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析蒙特卡洛多线程加速的原因
"""

import numpy as np
import time
import concurrent.futures
import os

def black_scholes_monte_carlo(S0, K, T, r, sigma, num_simulations, num_steps):
    """原始的蒙特卡洛函数"""
    dt = T / num_steps
    z = np.random.standard_normal((num_simulations, num_steps))
    daily_returns = np.exp((r - 0.5 * sigma**2) * dt + sigma * np.sqrt(dt) * z)
    price_paths = S0 * np.cumprod(daily_returns, axis=1)
    ST = price_paths[:, -1]
    payoffs = np.maximum(ST - K, 0)
    option_price = np.exp(-r * T) * np.mean(payoffs)
    return option_price

def test_different_sizes():
    """测试不同数据大小对性能的影响"""
    S0, K, T, r, sigma, num_steps = 100, 105, 1.0, 0.05, 0.2, 252
    
    sizes = [100_000, 250_000, 500_000, 1_000_000]
    num_threads = 8
    
    print("=== 不同数据大小的性能测试 ===")
    print(f"{'大小':<12} {'单线程(s)':<12} {'多线程(s)':<12} {'加速比':<8}")
    print("-" * 50)
    
    for size in sizes:
        # 单线程测试
        start = time.perf_counter()
        single_result = black_scholes_monte_carlo(S0, K, T, r, sigma, size, num_steps)
        single_time = time.perf_counter() - start
        
        # 多线程测试
        simulations_per_thread = size // num_threads
        start = time.perf_counter()
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(black_scholes_monte_carlo, S0, K, T, r, sigma, simulations_per_thread, num_steps) for _ in range(num_threads)]
            results = [f.result() for f in futures]
        multi_time = time.perf_counter() - start
        
        speedup = single_time / multi_time
        print(f"{size:<12} {single_time:<12.4f} {multi_time:<12.4f} {speedup:<8.2f}")

def test_memory_allocation():
    """测试内存分配对性能的影响"""
    print("\n=== 内存分配模式测试 ===")
    
    S0, K, T, r, sigma, num_steps = 100, 105, 1.0, 0.05, 0.2, 252
    total_sims = 1_000_000
    num_threads = 8
    sims_per_thread = total_sims // num_threads
    
    # 方法1：一次性分配大数组
    print("方法1：一次性分配大数组")
    start = time.perf_counter()
    result1 = black_scholes_monte_carlo(S0, K, T, r, sigma, total_sims, num_steps)
    time1 = time.perf_counter() - start
    print(f"耗时: {time1:.4f} 秒")
    
    # 方法2：分批分配小数组（模拟多线程）
    print("方法2：分批分配小数组")
    start = time.perf_counter()
    results = []
    for _ in range(num_threads):
        result = black_scholes_monte_carlo(S0, K, T, r, sigma, sims_per_thread, num_steps)
        results.append(result)
    result2 = np.mean(results)
    time2 = time.perf_counter() - start
    print(f"耗时: {time2:.4f} 秒")
    
    # 方法3：真正的多线程
    print("方法3：真正的多线程")
    start = time.perf_counter()
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(black_scholes_monte_carlo, S0, K, T, r, sigma, sims_per_thread, num_steps) for _ in range(num_threads)]
        results = [f.result() for f in futures]
    result3 = np.mean(results)
    time3 = time.perf_counter() - start
    print(f"耗时: {time3:.4f} 秒")
    
    print(f"\n结果对比:")
    print(f"方法1结果: {result1:.4f}")
    print(f"方法2结果: {result2:.4f}")
    print(f"方法3结果: {result3:.4f}")
    print(f"\n性能对比:")
    print(f"方法2 vs 方法1: {time1/time2:.2f}x")
    print(f"方法3 vs 方法1: {time1/time3:.2f}x")

def test_numpy_operations():
    """测试具体的 NumPy 操作"""
    print("\n=== NumPy 操作性能测试 ===")
    
    size = 1_000_000
    steps = 252
    
    def test_operation(name, operation_func, *args):
        # 单线程
        start = time.perf_counter()
        result1 = operation_func(*args)
        single_time = time.perf_counter() - start
        
        # 多线程（分割数据）
        chunk_size = size // 4
        start = time.perf_counter()
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            if len(args) == 2:  # 二维数组
                chunks = [args[0][i*chunk_size:(i+1)*chunk_size] for i in range(4)]
                futures = [executor.submit(operation_func, chunk, args[1]) for chunk in chunks]
            else:  # 一维数组
                chunks = [args[0][i*chunk_size:(i+1)*chunk_size] for i in range(4)]
                futures = [executor.submit(operation_func, chunk) for chunk in chunks]
            results = [f.result() for f in futures]
        multi_time = time.perf_counter() - start
        
        speedup = single_time / multi_time
        print(f"{name:<20} 单线程: {single_time:.4f}s, 多线程: {multi_time:.4f}s, 加速比: {speedup:.2f}x")
    
    # 准备测试数据
    z = np.random.standard_normal((size, steps))
    data_1d = np.random.random(size)
    
    # 测试各种操作
    test_operation("随机数生成", lambda s, st: np.random.standard_normal((s, st)), size//4, steps)
    test_operation("指数运算", lambda x: np.exp(x), data_1d)
    test_operation("累积乘积", lambda x: np.cumprod(x, axis=1), z)
    test_operation("最大值运算", lambda x: np.maximum(x, 0), data_1d)

def main():
    print("详细分析蒙特卡洛多线程性能")
    print("=" * 60)
    
    # 测试1：不同数据大小
    test_different_sizes()
    
    # 测试2：内存分配模式
    test_memory_allocation()
    
    # 测试3：具体 NumPy 操作
    test_numpy_operations()
    
    print("\n" + "=" * 60)
    print("结论:")
    print("1. 如果多线程确实比单线程快，可能的原因包括:")
    print("   - 更好的内存缓存局部性（小数据块）")
    print("   - NumPy 2.3.0 的特定优化")
    print("   - 某些 NumPy 操作确实释放了 GIL")
    print("2. 在 free-threading Python 中，这种加速会更加明显")
    print("3. 多进程仍然是 CPU 密集型任务的最可靠并行方案")

if __name__ == "__main__":
    main()
