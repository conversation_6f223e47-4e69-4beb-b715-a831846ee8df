# === Jupyter Notebook 中文字体修复代码 ===
# 请将此代码复制到 Notebook 的第一个 cell 中运行

import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np
import os

# 强制重置和配置
mpl.rcdefaults()
plt.rcParams.update({
    'font.sans-serif': ['WenQuanYi Micro Hei', '文泉驿微米黑', 'Arial Unicode MS', 'SimHei'],
    'axes.unicode_minus': False,
    'font.size': 12
})

# 验证配置
print("✅ 字体配置完成")
print("当前字体:", plt.rcParams['font.sans-serif'][:2])

# 测试中文显示
def test_chinese():
    fig, ax = plt.subplots(figsize=(8, 5))
    x = np.linspace(0, 10, 100)
    ax.plot(x, np.sin(x), label='正弦波')
    ax.set_title('中文字体测试 - 低延迟趋势线分析')
    ax.set_xlabel('时间轴')
    ax.set_ylabel('价格变化')
    ax.legend()
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    # 检查字体
    title_font = ax.title.get_fontname()
    print(f"标题使用字体: {title_font}")
    if 'WenQuanYi' in title_font or '微米黑' in title_font:
        print("✅ 中文字体显示成功！")
    else:
        print("❌ 中文字体显示失败，请检查字体安装")

# 运行测试
test_chinese()