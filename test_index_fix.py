#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from backtest_reporter import create_backtest_report

# 创建测试数据
np.random.seed(42)
dates = pd.date_range('2020-01-01', periods=20, freq='D')
prices = pd.Series(100 + np.cumsum(np.random.randn(20) * 0.5), index=dates)

# 创建包含连续信号的策略
def test_strategy(data):
    signals = pd.Series(0, index=data.index)
    # 连续买入信号
    signals.iloc[2:5] = 1  # 第3-5天连续买入
    signals.iloc[8:10] = 1  # 第9-10天连续买入
    # 连续卖出信号
    signals.iloc[12:15] = -1  # 第13-15天连续卖出
    return signals

# 生成信号
signals = test_strategy(prices)

print("原始信号:")
for i, (date, signal) in enumerate(signals.items()):
    if signal != 0:
        print(f"索引 {i}: {date.strftime('%Y-%m-%d')} = {signal}")

# 测试过滤函数
def filter_consecutive_signals(signal_series, target_value):
    """过滤连续信号，只保留每组连续信号的第一个"""
    # 使用向量化操作：检测信号变化点
    mask = (signal_series == target_value) & (signal_series.shift(1) != target_value)
    # 返回整数索引位置
    return mask[mask].index.get_indexer(signal_series.index).tolist()

buy_indices = filter_consecutive_signals(signals, 1)
sell_indices = filter_consecutive_signals(signals, -1)

print("\n过滤后的买入索引:", buy_indices)
print("过滤后的卖出索引:", sell_indices)

print("\n买入点详情:")
for idx in buy_indices:
    print(f"索引 {idx}: {signals.index[idx].strftime('%Y-%m-%d')} = {signals.iloc[idx]}")

print("\n卖出点详情:")
for idx in sell_indices:
    print(f"索引 {idx}: {signals.index[idx].strftime('%Y-%m-%d')} = {signals.iloc[idx]}")

# 生成报告验证图表
print("\n生成回测报告...")
# 创建包含基准数据的DataFrame
test_df = pd.DataFrame({
    'open': prices,
    'benchmark': prices * 1.001  # 简单基准
})

report = create_backtest_report(
    test_df, 
    test_strategy, 
    args={},
    title="索引修复测试"
)

print("测试完成！")