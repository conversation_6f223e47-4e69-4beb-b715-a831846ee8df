#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

# 创建简单测试数据
dates = pd.date_range('2020-01-01', periods=10, freq='D')
signals = pd.Series([0, 0, 1, 1, 1, 0, -1, -1, 0, 1], index=dates)

print("测试信号:")
for i, (date, signal) in enumerate(signals.items()):
    print(f"索引 {i}: {date.strftime('%Y-%m-%d')} = {signal}")

# 测试向量化过滤函数
def filter_consecutive_signals(signal_series, target_value):
    """过滤连续信号，只保留每组连续信号的第一个"""
    # 使用向量化操作：检测信号变化点
    mask = (signal_series == target_value) & (signal_series.shift(1) != target_value)
    # 返回整数索引位置
    return [i for i, val in enumerate(mask) if val]

buy_indices = filter_consecutive_signals(signals, 1)
sell_indices = filter_consecutive_signals(signals, -1)

print("\n过滤后的买入索引:", buy_indices)
print("过滤后的卖出索引:", sell_indices)

print("\n买入点详情:")
for idx in buy_indices:
    print(f"索引 {idx}: {signals.index[idx].strftime('%Y-%m-%d')} = {signals.iloc[idx]}")

print("\n卖出点详情:")
for idx in sell_indices:
    print(f"索引 {idx}: {signals.index[idx].strftime('%Y-%m-%d')} = {signals.iloc[idx]}")

print("\n预期结果:")
print("买入索引应该是: [2, 9] (第一个1和最后一个1)")
print("卖出索引应该是: [6] (第一个-1)")

print("\n测试完成！")