import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Callable, Tuple, Any

def backtest(df, calc_signal, args, price: str = "open", long_weight: float = 0.5, short_weight: float = 0.5, long_short=True):
    """原始回测函数"""
    df = df.copy()
    df["signal"] = calc_signal(df, *args)
    df["signal"] = df["signal"].fillna(0)
    df["signal_shifted"] = df["signal"].shift(1)
    df["benchmark"] = df[price].pct_change()
    
    df['long_return'] = np.where(df['signal_shifted'] == 1, df['benchmark'], 0)
    df['short_return'] = np.where(df['signal_shifted'] == -1, -df['benchmark'], 0)
    df["long_short_return"] = df['long_return'] * long_weight + df['short_return'] * short_weight
    
    return df

def calculate_metrics(returns: pd.Series, benchmark_returns: pd.Series) -> dict:
    """计算策略表现指标"""
    # 基本统计
    total_return = (1 + returns).prod() - 1
    benchmark_total = (1 + benchmark_returns).prod() - 1
    
    # 年化收益率
    trading_days = len(returns)
    annual_return = (1 + total_return) ** (252 / trading_days) - 1
    benchmark_annual = (1 + benchmark_total) ** (252 / trading_days) - 1
    
    # 胜率
    win_rate = (returns > 0).mean()
    benchmark_win_rate = (benchmark_returns > 0).mean()
    
    # 波动率
    volatility = returns.std() * np.sqrt(252)
    benchmark_vol = benchmark_returns.std() * np.sqrt(252)
    
    # 夏普比率
    sharpe = annual_return / volatility if volatility > 0 else 0
    benchmark_sharpe = benchmark_annual / benchmark_vol if benchmark_vol > 0 else 0
    
    # 最大回撤
    cumulative = (1 + returns).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdown = (cumulative - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    benchmark_cum = (1 + benchmark_returns).cumprod()
    benchmark_rolling_max = benchmark_cum.expanding().max()
    benchmark_drawdown = (benchmark_cum - benchmark_rolling_max) / benchmark_rolling_max
    benchmark_max_dd = benchmark_drawdown.min()
    
    # Sortino比率
    downside_returns = returns[returns < 0]
    downside_vol = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
    sortino = annual_return / downside_vol if downside_vol > 0 else 0
    
    benchmark_downside = benchmark_returns[benchmark_returns < 0]
    benchmark_downside_vol = benchmark_downside.std() * np.sqrt(252) if len(benchmark_downside) > 0 else 0
    benchmark_sortino = benchmark_annual / benchmark_downside_vol if benchmark_downside_vol > 0 else 0
    
    return {
        '策略': {
            '年化收益率': f"{annual_return:.2%}",
            '胜率': f"{win_rate:.2%}",
            '夏普比率': f"{sharpe:.3f}",
            '最大回撤': f"{max_drawdown:.2%}",
            'Sortino比率': f"{sortino:.3f}"
        },
        'Benchmark': {
            '年化收益率': f"{benchmark_annual:.2%}",
            '胜率': f"{benchmark_win_rate:.2%}",
            '夏普比率': f"{benchmark_sharpe:.3f}",
            '最大回撤': f"{benchmark_max_dd:.2%}",
            'Sortino比率': f"{benchmark_sortino:.3f}"
        }
    }

def create_backtest_report(df, calc_signal, args, price: str = "open", 
                          long_weight: float = 0.5, short_weight: float = 0.5, 
                          long_short: bool = True, title: str = "回测报告") -> go.Figure:
    """创建完整的回测报告"""
    
    # 执行回测
    result_df = backtest(df, calc_signal, args, price, long_weight, short_weight, long_short)
    
    # 选择策略收益
    if long_short:
        strategy_col = "long_short_return"
    else:
        strategy_col = 'long_return'
    
    # 去除NaN值
    valid_data = result_df.dropna(subset=[strategy_col, 'benchmark'])
    strategy_returns = valid_data[strategy_col]
    benchmark_returns = valid_data['benchmark']
    signals = valid_data['signal_shifted']
    prices = valid_data[price]
    
    # 计算累积收益
    strategy_cumulative = (1 + strategy_returns).cumprod()
    benchmark_cumulative = (1 + benchmark_returns).cumprod()
    
    # 计算指标
    metrics = calculate_metrics(strategy_returns, benchmark_returns)
    
    # 处理时间索引
    if isinstance(valid_data.index, pd.DatetimeIndex):
        # 创建分类索引以去掉非交易日
        x_axis = list(range(len(valid_data)))
        x_labels = [d.strftime('%Y-%m-%d') for d in valid_data.index]
        x_tickvals = list(range(0, len(valid_data), max(1, len(valid_data)//10)))
        x_ticktext = [x_labels[i] for i in x_tickvals]
    else:
        x_axis = valid_data.index
        x_labels = [str(x) for x in x_axis]
        x_tickvals = x_axis
        x_ticktext = x_labels
    
    # 创建子图
    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=['累积收益对比', '策略指标对比'],
        vertical_spacing=0.15,
        row_heights=[0.7, 0.3],
        specs=[[{"secondary_y": False}], [{"type": "table"}]]
    )
    
    # 第一个子图：累积收益曲线
    fig.add_trace(
        go.Scatter(
            x=x_axis,
            y=strategy_cumulative,
            name='策略收益',
            line=dict(color='blue', width=2),
            hovertemplate='日期: %{text}<br>累积收益: %{y:.2%}<extra></extra>',
            text=x_labels
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=x_axis,
            y=benchmark_cumulative,
            name='基准收益',
            line=dict(color='gray', width=1, dash='dash'),
            hovertemplate='日期: %{text}<br>基准收益: %{y:.2%}<extra></extra>',
            text=x_labels
        ),
        row=1, col=1
    )
    
    # 过滤连续信号，只保留每组连续信号的第一个
    def filter_consecutive_signals(signal_series, target_value):
        """过滤连续信号，只保留每组连续信号的第一个"""
        # 使用向量化操作：检测信号变化点
        mask = (signal_series == target_value) & (signal_series.shift(1) != target_value)
        # 返回整数索引位置
        return [i for i, val in enumerate(mask) if val]
    
    # 添加买卖标记
    buy_indices = filter_consecutive_signals(signals, 1)
    sell_indices = filter_consecutive_signals(signals, -1)
    
    if buy_indices:
        buy_prices = [strategy_cumulative.iloc[i] for i in buy_indices]
        buy_dates = [x_labels[i] for i in buy_indices]
        buy_actual_prices = [prices.iloc[i] for i in buy_indices]
        
        fig.add_trace(
            go.Scatter(
                x=buy_indices,
                y=buy_prices,
                mode='markers',
                name='买入信号',
                marker=dict(symbol='triangle-up', size=10, color='green'),
                hovertemplate='买入<br>日期: %{text}<br>价格: %{customdata:.2f}<br>累积收益: %{y:.2%}<extra></extra>',
                text=buy_dates,
                customdata=buy_actual_prices
            ),
            row=1, col=1
        )
    
    if sell_indices:
        sell_prices = [strategy_cumulative.iloc[i] for i in sell_indices]
        sell_dates = [x_labels[i] for i in sell_indices]
        sell_actual_prices = [prices.iloc[i] for i in sell_indices]
        
        fig.add_trace(
            go.Scatter(
                x=sell_indices,
                y=sell_prices,
                mode='markers',
                name='卖出信号',
                marker=dict(symbol='triangle-down', size=10, color='red'),
                hovertemplate='卖出<br>日期: %{text}<br>价格: %{customdata:.2f}<br>累积收益: %{y:.2%}<extra></extra>',
                text=sell_dates,
                customdata=sell_actual_prices
            ),
            row=1, col=1
        )
    
    # 第二个子图：指标对比表格
    metrics_data = []
    for metric in ['年化收益率', '胜率', '夏普比率', '最大回撤', 'Sortino比率']:
        metrics_data.append([metric, metrics['策略'][metric], metrics['Benchmark'][metric]])
    
    fig.add_trace(
        go.Table(
            header=dict(
                values=['指标', '策略', 'Benchmark'],
                fill_color='lightblue',
                align='center',
                font=dict(size=12, color='black')
            ),
            cells=dict(
                values=list(zip(*metrics_data)),
                fill_color='white',
                align='center',
                font=dict(size=11)
            )
        ),
        row=2, col=1
    )
    
    # 更新布局
    fig.update_layout(
        title=dict(
            text=title,
            x=0.5,
            font=dict(size=16)
        ),
        height=800,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    # 更新x轴
    fig.update_xaxes(
        title_text="交易日",
        tickvals=x_tickvals,
        ticktext=x_ticktext,
        tickangle=45,
        row=1, col=1
    )
    
    # 更新y轴
    fig.update_yaxes(
        title_text="累积收益率",
        tickformat=".1%",
        row=1, col=1
    )
    
    return fig

# 示例信号函数
def simple_ma_signal(df: pd.DataFrame, short_window: int = 5, long_window: int = 20) -> pd.Series:
    """简单移动平均交叉信号"""
    short_ma = df['close'].rolling(window=short_window).mean()
    long_ma = df['close'].rolling(window=long_window).mean()
    
    signals = pd.Series(0, index=df.index)
    signals[short_ma > long_ma] = 1
    signals[short_ma < long_ma] = -1
    
    return signals

# 使用示例
if __name__ == "__main__":
    # 生成示例数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    prices = 100 * (1 + np.random.randn(100) * 0.02).cumprod()
    
    sample_df = pd.DataFrame({
        'open': prices,
        'close': prices * (1 + np.random.randn(100) * 0.01)
    }, index=dates)
    
    # 创建报告
    fig = create_backtest_report(
        sample_df, 
        simple_ma_signal, 
        (5, 20),  # 参数
        price='close',
        long_short=True,
        title="移动平均策略回测报告"
    )
    
    fig.show()