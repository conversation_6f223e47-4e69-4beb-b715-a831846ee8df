#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试和配置 matplotlib 中文字体显示
使用 WenQuanYi 微米黑字体
"""

import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.font_manager import FontProperties, fontManager
import numpy as np

def check_available_fonts():
    """检查可用的中文字体"""
    print("=== 检查系统中的 WenQuanYi 字体 ===")
    fonts = [f for f in fontManager.ttflist if 'WenQuanYi' in f.name or '微米黑' in f.name]
    for font in fonts:
        print(f"字体名称: {font.name}")
        print(f"字体文件: {font.fname}")
        print(f"字体样式: {font.style}")
        print("-" * 50)
    return fonts

def configure_wenquanyi_font():
    """配置 WenQuanYi 微米黑字体"""
    print("=== 配置 WenQuanYi 微米黑字体 ===")
    
    # 方法1: 直接设置字体族
    plt.rcParams['font.sans-serif'] = [
        'WenQuanYi Micro Hei',  # 英文名
        '文泉驿微米黑',           # 中文名
        'Arial Unicode MS',     # 备用字体
        'DejaVu Sans'          # 默认备用
    ]
    plt.rcParams['axes.unicode_minus'] = False
    
    print("已设置字体族:", plt.rcParams['font.sans-serif'])

def test_chinese_display():
    """测试中文显示效果"""
    print("=== 测试中文显示 ===")
    
    # 创建测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 绘制图形
    ax.plot(x, y1, label='正弦波 (sin)', linewidth=2, color='blue')
    ax.plot(x, y2, label='余弦波 (cos)', linewidth=2, color='red')
    
    # 设置标题和标签
    ax.set_title('量化交易中的技术指标示例 - WenQuanYi微米黑字体测试', fontsize=16, pad=20)
    ax.set_xlabel('时间 (秒)', fontsize=12)
    ax.set_ylabel('价格波动', fontsize=12)
    
    # 添加图例
    ax.legend(fontsize=12)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 添加注释
    ax.annotate('峰值点', xy=(np.pi/2, 1), xytext=(2, 1.2),
                arrowprops=dict(arrowstyle='->', color='green'),
                fontsize=10, color='green')
    
    # 保存图片
    plt.tight_layout()
    plt.savefig('chinese_font_test.png', dpi=150, bbox_inches='tight')
    print("测试图片已保存为: chinese_font_test.png")
    
    # 显示图形
    plt.show()

def create_matplotlib_config():
    """创建 matplotlib 配置文件"""
    config_dir = mpl.get_configdir()
    config_file = f"{config_dir}/matplotlibrc"
    
    config_content = """
# matplotlib 中文字体配置
font.family: sans-serif
font.sans-serif: WenQuanYi Micro Hei, 文泉驿微米黑, Arial Unicode MS, DejaVu Sans
axes.unicode_minus: False

# 其他常用配置
figure.figsize: 10, 6
figure.dpi: 100
savefig.dpi: 150
font.size: 12
"""
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content.strip())
        print(f"已创建配置文件: {config_file}")
    except Exception as e:
        print(f"创建配置文件失败: {e}")

def main():
    """主函数"""
    print("matplotlib 中文字体配置工具")
    print("=" * 50)
    
    # 检查可用字体
    fonts = check_available_fonts()
    
    if not fonts:
        print("❌ 未找到 WenQuanYi 字体，请先安装:")
        print("brew install font-wenquanyi-micro-hei")
        return
    
    print("✅ 找到 WenQuanYi 字体")
    
    # 配置字体
    configure_wenquanyi_font()
    
    # 测试显示
    test_chinese_display()
    
    # 创建配置文件
    create_matplotlib_config()
    
    print("\n=== 配置完成 ===")
    print("如果中文仍无法显示，请:")
    print("1. 重启 Python 内核")
    print("2. 清除字体缓存: rm -rf ~/.matplotlib/fontlist-*.json")
    print("3. 重新运行此脚本")

if __name__ == "__main__":
    main()
