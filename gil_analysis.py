#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 GIL 对不同类型任务的影响
"""

import numpy as np
import time
import concurrent.futures
import threading

def pure_python_cpu_task(n):
    """纯 Python CPU 密集型任务 - 不会释放 GIL"""
    result = 0
    for i in range(n):
        result += i * i * i
    return result

def numpy_heavy_task(size):
    """NumPy 密集型任务 - 会释放 GIL"""
    # 模拟蒙特卡洛中的操作
    z = np.random.standard_normal((size, 252))
    returns = np.exp(0.05 * 1/252 + 0.2 * np.sqrt(1/252) * z)
    paths = 100 * np.cumprod(returns, axis=1)
    payoffs = np.maximum(paths[:, -1] - 105, 0)
    return np.mean(payoffs)

def mixed_task(n, size):
    """混合任务 - 既有 Python 计算又有 NumPy 操作"""
    # Python 部分
    python_result = 0
    for i in range(n):
        python_result += i * i
    
    # NumPy 部分
    numpy_result = numpy_heavy_task(size)
    
    return python_result + numpy_result

def benchmark_task(task_func, args, num_threads=1, task_name=""):
    """基准测试函数"""
    print(f"\n=== {task_name} ===")
    
    if num_threads == 1:
        # 单线程
        start = time.perf_counter()
        result = task_func(*args)
        end = time.perf_counter()
        single_time = end - start
        print(f"单线程耗时: {single_time:.4f} 秒")
        return single_time
    else:
        # 多线程
        start = time.perf_counter()
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(task_func, *args) for _ in range(num_threads)]
            results = [f.result() for f in futures]
        end = time.perf_counter()
        multi_time = end - start
        print(f"多线程({num_threads}线程)耗时: {multi_time:.4f} 秒")
        return multi_time

def main():
    print("GIL 影响分析 - 不同任务类型的多线程性能")
    print("=" * 60)
    
    num_cores = 4  # 使用 4 个线程进行测试
    
    # 1. 纯 Python CPU 密集型任务
    print("\n1. 纯 Python CPU 密集型任务（预期：多线程无加速或更慢）")
    python_args = (1_000_000,)
    single_python = benchmark_task(pure_python_cpu_task, python_args, 1, "纯Python任务-单线程")
    multi_python = benchmark_task(pure_python_cpu_task, python_args, num_cores, "纯Python任务-多线程")
    python_speedup = single_python / multi_python
    print(f"加速比: {python_speedup:.2f}x")
    
    # 2. NumPy 密集型任务
    print("\n2. NumPy 密集型任务（预期：多线程有一定加速）")
    numpy_args = (250_000,)  # 调整大小以获得合理的运行时间
    single_numpy = benchmark_task(numpy_heavy_task, numpy_args, 1, "NumPy任务-单线程")
    multi_numpy = benchmark_task(numpy_heavy_task, numpy_args, num_cores, "NumPy任务-多线程")
    numpy_speedup = single_numpy / multi_numpy
    print(f"加速比: {numpy_speedup:.2f}x")
    
    # 3. 混合任务
    print("\n3. 混合任务（预期：部分加速）")
    mixed_args = (100_000, 100_000)
    single_mixed = benchmark_task(mixed_task, mixed_args, 1, "混合任务-单线程")
    multi_mixed = benchmark_task(mixed_task, mixed_args, num_cores, "混合任务-多线程")
    mixed_speedup = single_mixed / multi_mixed
    print(f"加速比: {mixed_speedup:.2f}x")
    
    # 4. 原始蒙特卡洛任务（简化版）
    print("\n4. 蒙特卡洛期权定价（简化版）")
    def simple_monte_carlo(num_sims):
        S0, K, T, r, sigma = 100, 105, 1.0, 0.05, 0.2
        dt = T / 252
        z = np.random.standard_normal((num_sims, 252))
        returns = np.exp((r - 0.5 * sigma**2) * dt + sigma * np.sqrt(dt) * z)
        paths = S0 * np.cumprod(returns, axis=1)
        payoffs = np.maximum(paths[:, -1] - K, 0)
        return np.exp(-r * T) * np.mean(payoffs)
    
    mc_args = (200_000,)
    single_mc = benchmark_task(simple_monte_carlo, mc_args, 1, "蒙特卡洛-单线程")
    multi_mc = benchmark_task(simple_monte_carlo, mc_args, num_cores, "蒙特卡洛-多线程")
    mc_speedup = single_mc / multi_mc
    print(f"加速比: {mc_speedup:.2f}x")
    
    print("\n" + "=" * 60)
    print("总结:")
    print(f"1. 纯Python任务加速比: {python_speedup:.2f}x (预期 ≤ 1.0)")
    print(f"2. NumPy任务加速比: {numpy_speedup:.2f}x (预期 > 1.0)")
    print(f"3. 混合任务加速比: {mixed_speedup:.2f}x (预期 1.0-2.0)")
    print(f"4. 蒙特卡洛加速比: {mc_speedup:.2f}x (预期 > 1.0)")
    print("\n解释:")
    print("- NumPy 操作会释放 GIL，允许真正的并行执行")
    print("- 纯 Python 操作受 GIL 限制，无法并行")
    print("- 蒙特卡洛模拟主要是 NumPy 操作，因此能获得加速")

if __name__ == "__main__":
    main()
