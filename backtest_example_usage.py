import pandas as pd
import numpy as np
import yfinance as yf
from backtest_reporter import create_backtest_report, simple_ma_signal

# 示例1：使用真实股票数据
def example_with_real_data():
    """使用真实股票数据进行回测"""
    print("正在下载股票数据...")
    
    # 下载苹果股票数据
    ticker = "AAPL"
    data = yf.download(ticker, start="2023-01-01", end="2024-01-01")
    
    # 重命名列名为小写
    data.columns = [col.lower() for col in data.columns]
    
    # 创建回测报告
    fig = create_backtest_report(
        data,
        simple_ma_signal,
        (5, 20),  # 短期5日，长期20日均线
        price='close',
        long_short=True,
        title=f"{ticker} 移动平均策略回测报告"
    )
    
    fig.show()
    return fig

# 示例2：自定义RSI策略
def rsi_signal(df: pd.DataFrame, window: int = 14, oversold: float = 30, overbought: float = 70) -> pd.Series:
    """RSI策略信号"""
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    signals = pd.Series(0, index=df.index)
    signals[rsi < oversold] = 1   # 超卖，买入
    signals[rsi > overbought] = -1  # 超买，卖出
    
    return signals

def example_with_rsi_strategy():
    """使用RSI策略进行回测"""
    print("正在下载数据并测试RSI策略...")
    
    # 下载数据
    data = yf.download("TSLA", start="2023-01-01", end="2024-01-01")
    data.columns = [col.lower() for col in data.columns]
    
    # 创建RSI策略回测报告
    fig = create_backtest_report(
        data,
        rsi_signal,
        (14, 30, 70),  # RSI参数：窗口14，超卖30，超买70
        price='close',
        long_short=True,
        title="TSLA RSI策略回测报告"
    )
    
    fig.show()
    return fig

# 示例3：布林带策略
def bollinger_signal(df: pd.DataFrame, window: int = 20, num_std: float = 2.0) -> pd.Series:
    """布林带策略信号"""
    sma = df['close'].rolling(window=window).mean()
    std = df['close'].rolling(window=window).std()
    
    upper_band = sma + (std * num_std)
    lower_band = sma - (std * num_std)
    
    signals = pd.Series(0, index=df.index)
    signals[df['close'] <= lower_band] = 1   # 价格触及下轨，买入
    signals[df['close'] >= upper_band] = -1  # 价格触及上轨，卖出
    
    return signals

def example_with_bollinger_strategy():
    """使用布林带策略进行回测"""
    print("正在测试布林带策略...")
    
    # 下载数据
    data = yf.download("MSFT", start="2023-01-01", end="2024-01-01")
    data.columns = [col.lower() for col in data.columns]
    
    # 创建布林带策略回测报告
    fig = create_backtest_report(
        data,
        bollinger_signal,
        (20, 2.0),  # 布林带参数：20日均线，2倍标准差
        price='close',
        long_short=True,
        title="MSFT 布林带策略回测报告"
    )
    
    fig.show()
    return fig

# 示例4：仅做多策略对比
def compare_long_vs_long_short():
    """对比仅做多和多空策略的差异"""
    print("对比仅做多 vs 多空策略...")
    
    # 下载数据
    data = yf.download("SPY", start="2023-01-01", end="2024-01-01")
    data.columns = [col.lower() for col in data.columns]
    
    # 仅做多策略
    fig1 = create_backtest_report(
        data,
        simple_ma_signal,
        (10, 30),
        price='close',
        long_short=False,  # 仅做多
        title="SPY 移动平均策略 - 仅做多"
    )
    
    # 多空策略
    fig2 = create_backtest_report(
        data,
        simple_ma_signal,
        (10, 30),
        price='close',
        long_short=True,  # 多空组合
        title="SPY 移动平均策略 - 多空组合"
    )
    
    fig1.show()
    fig2.show()
    
    return fig1, fig2

# 示例5：使用模拟数据
def example_with_simulated_data():
    """使用模拟数据进行回测"""
    print("使用模拟数据进行回测...")
    
    # 生成模拟数据
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=252, freq='D')  # 一年交易日
    
    # 生成带趋势的价格数据
    returns = np.random.normal(0.0005, 0.02, 252)  # 日收益率
    trend = np.linspace(0, 0.1, 252)  # 添加上升趋势
    returns += trend / 252
    
    prices = 100 * np.cumprod(1 + returns)
    
    # 添加一些噪音
    open_prices = prices * (1 + np.random.normal(0, 0.005, 252))
    high_prices = np.maximum(open_prices, prices) * (1 + np.abs(np.random.normal(0, 0.01, 252)))
    low_prices = np.minimum(open_prices, prices) * (1 - np.abs(np.random.normal(0, 0.01, 252)))
    
    sim_data = pd.DataFrame({
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': prices,
        'volume': np.random.randint(1000000, 5000000, 252)
    }, index=dates)
    
    # 创建回测报告
    fig = create_backtest_report(
        sim_data,
        simple_ma_signal,
        (5, 20),
        price='close',
        long_short=True,
        title="模拟数据 移动平均策略回测"
    )
    
    fig.show()
    return fig

def main():
    """主函数 - 运行所有示例"""
    print("=" * 50)
    print("回测报告生成器 - 使用示例")
    print("=" * 50)
    
    examples = {
        "1": ("真实股票数据 (AAPL)", example_with_real_data),
        "2": ("RSI策略 (TSLA)", example_with_rsi_strategy),
        "3": ("布林带策略 (MSFT)", example_with_bollinger_strategy),
        "4": ("仅做多 vs 多空对比 (SPY)", compare_long_vs_long_short),
        "5": ("模拟数据测试", example_with_simulated_data)
    }
    
    print("\n请选择要运行的示例:")
    for key, (desc, _) in examples.items():
        print(f"{key}. {desc}")
    print("0. 运行所有示例")
    
    choice = input("\n请输入选择 (0-5): ").strip()
    
    if choice == "0":
        print("\n运行所有示例...")
        for key, (desc, func) in examples.items():
            print(f"\n{'='*20} {desc} {'='*20}")
            try:
                func()
            except Exception as e:
                print(f"示例 {key} 运行失败: {e}")
    elif choice in examples:
        desc, func = examples[choice]
        print(f"\n运行示例: {desc}")
        try:
            func()
        except Exception as e:
            print(f"示例运行失败: {e}")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()