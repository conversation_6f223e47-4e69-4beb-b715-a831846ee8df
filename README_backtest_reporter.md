# 回测报告生成器

基于用户提供的 `backtest` 函数，创建了一个完整的回测报告生成器，支持策略与基准的全面对比分析。

## 核心功能

### 1. 基本指标报表
- **年化收益率**：策略与基准对比
- **胜率**：盈利交易占比
- **夏普比率**：风险调整后收益
- **最大回撤**：最大亏损幅度
- **Sortino比率**：下行风险调整收益

### 2. 可视化图表
- **累积收益曲线**：策略 vs 基准对比
- **交易标记**：买入/卖出信号点
- **智能信号过滤**：连续相同信号只显示第一个，避免图表混乱
- **悬停信息**：显示日期、价格、收益率
- **连续时间轴**：自动处理非交易日

### 3. 灵活配置
- 支持纯多头和多空组合策略
- 可调整多空权重
- 支持不同价格字段（open/close）
- 自动处理时间序列数据

## 快速开始

### 安装依赖
```bash
pip install pandas numpy plotly yfinance
```

### 基本使用

```python
from backtest_reporter import create_backtest_report, simple_ma_signal
import yfinance as yf

# 下载股票数据
data = yf.download("AAPL", start="2023-01-01", end="2024-01-01")
data.columns = [col.lower() for col in data.columns]

# 创建回测报告
fig = create_backtest_report(
    data,
    simple_ma_signal,  # 信号函数
    (5, 20),          # 信号函数参数
    price='close',     # 使用收盘价
    long_short=True,   # 多空组合策略
    title="AAPL 移动平均策略回测"
)

fig.show()
```

## 核心函数说明

### `create_backtest_report()`

**参数说明：**
- `df`: 价格数据DataFrame，需包含OHLC列
- `calc_signal`: 信号计算函数
- `args`: 信号函数的参数元组
- `price`: 使用的价格字段，默认'open'
- `long_weight`: 多头权重，默认0.5
- `short_weight`: 空头权重，默认0.5
- `long_short`: 是否多空组合，默认True
- `title`: 报告标题

**返回值：**
- Plotly图表对象，包含累积收益曲线和指标对比表

### 信号函数规范

信号函数必须遵循以下格式：

```python
def your_signal_function(df: pd.DataFrame, *args) -> pd.Series:
    """
    自定义信号函数
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含OHLC数据的DataFrame
    *args : tuple
        信号函数的参数
        
    Returns:
    --------
    pd.Series : 交易信号
        1: 买入信号
        -1: 卖出信号
        0: 无信号
    """
    # 你的信号计算逻辑
    signals = pd.Series(0, index=df.index)
    # ... 计算逻辑 ...
    return signals
```

## 内置策略示例

### 1. 移动平均交叉策略

```python
def simple_ma_signal(df, short_window=5, long_window=20):
    """移动平均交叉策略"""
    short_ma = df['close'].rolling(window=short_window).mean()
    long_ma = df['close'].rolling(window=long_window).mean()
    
    signals = pd.Series(0, index=df.index)
    signals[short_ma > long_ma] = 1   # 金叉买入
    signals[short_ma < long_ma] = -1  # 死叉卖出
    
    return signals
```

### 2. RSI策略

```python
def rsi_signal(df, window=14, oversold=30, overbought=70):
    """RSI超买超卖策略"""
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    signals = pd.Series(0, index=df.index)
    signals[rsi < oversold] = 1   # 超卖买入
    signals[rsi > overbought] = -1  # 超买卖出
    
    return signals
```

### 3. 布林带策略

```python
def bollinger_signal(df, window=20, num_std=2.0):
    """布林带策略"""
    sma = df['close'].rolling(window=window).mean()
    std = df['close'].rolling(window=window).std()
    
    upper_band = sma + (std * num_std)
    lower_band = sma - (std * num_std)
    
    signals = pd.Series(0, index=df.index)
    signals[df['close'] <= lower_band] = 1   # 触及下轨买入
    signals[df['close'] >= upper_band] = -1  # 触及上轨卖出
    
    return signals
```

## 使用示例

### 运行完整示例

```bash
python backtest_example_usage.py
```

示例包括：
1. 真实股票数据回测 (AAPL)
2. RSI策略测试 (TSLA)
3. 布林带策略测试 (MSFT)
4. 仅做多 vs 多空对比 (SPY)
5. 模拟数据测试

### 自定义策略示例

```python
# 动量策略
def momentum_signal(df, lookback=10, threshold=0.02):
    """动量策略：基于过去N日收益率"""
    returns = df['close'].pct_change(lookback)
    
    signals = pd.Series(0, index=df.index)
    signals[returns > threshold] = 1   # 强势买入
    signals[returns < -threshold] = -1  # 弱势卖出
    
    return signals

# 使用动量策略
fig = create_backtest_report(
    data,
    momentum_signal,
    (10, 0.02),  # 10日动量，2%阈值
    long_short=True,
    title="动量策略回测"
)
fig.show()
```

## 报告解读

### 指标含义

**年化收益率**
- 策略的年化收益表现
- 与基准（买入持有）对比

**胜率**
- 盈利交易日占总交易日比例
- 高胜率不一定意味着高收益

**夏普比率**
- 单位风险的超额收益
- 一般认为 >1 为良好，>2 为优秀

**最大回撤**
- 从峰值到谷值的最大跌幅
- 反映策略的风险控制能力

**Sortino比率**
- 只考虑下行风险的收益比率
- 比夏普比率更关注亏损风险

### 图表功能

**交互功能**
- 鼠标悬停查看详细信息
- 缩放和平移查看特定时期
- 点击图例隐藏/显示特定曲线

**买卖标记**
- 绿色向上三角：买入信号
- 红色向下三角：卖出信号
- **智能过滤**：连续相同信号只显示第一个，避免重复标记
- 悬停显示具体日期和价格

## 注意事项

### 1. 数据要求
- DataFrame必须包含所需的价格列
- 索引建议使用DatetimeIndex
- 数据应该是连续的交易日数据

### 2. 信号处理
- 信号会自动进行shift(1)处理，避免前瞻偏差
- 信号值：1(买入)、-1(卖出)、0(无信号)
- NaN值会被自动填充为0
- **连续信号过滤**：连续出现的相同信号只显示第一个，减少图表噪音

### 3. 策略类型
- `long_short=True`：多空组合，可做多做空
- `long_short=False`：仅做多，忽略卖出信号

### 4. 性能考虑
- 大数据集可能影响图表渲染速度
- 建议单次回测数据不超过1000个交易日

## 扩展建议

### 1. 添加更多指标
```python
# 可以扩展calculate_metrics函数
# 添加信息比率、卡尔玛比率等
```

### 2. 多策略对比
```python
# 可以修改为支持多个策略同时对比
# 在同一图表中显示多条策略曲线
```

### 3. 风险管理
```python
# 添加止损止盈逻辑
# 仓位管理和资金管理
```

### 4. 交易成本
```python
# 考虑手续费和滑点
# 更真实的回测结果
```

## 常见问题

**Q: 为什么我的策略收益为负？**
A: 检查信号函数逻辑，确保买卖信号正确。负收益可能是正常的市场表现。

**Q: 如何处理缺失数据？**
A: 函数会自动删除包含NaN的行，建议预先清理数据。

**Q: 可以用于高频数据吗？**
A: 当前版本适用于日频数据，高频数据需要考虑更多细节。

**Q: 如何保存报告？**
A: 使用Plotly的导出功能：`fig.write_html("report.html")` 或 `fig.write_image("report.png")`

---

这个回测报告生成器专为量化策略分析设计，代码简洁高效，适合快速验证交易想法和策略效果。