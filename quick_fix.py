#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复 Jupyter Notebook 中文字体问题
关键步骤和诊断
"""

import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.font_manager import fontManager
import numpy as np
import os

def diagnose_font_issue():
    """诊断字体问题"""
    print("=== 字体问题诊断 ===")
    
    # 1. 检查当前字体设置
    print("1. 当前字体设置:")
    print(f"   font.sans-serif: {mpl.rcParams['font.sans-serif']}")
    print(f"   axes.unicode_minus: {mpl.rcParams['axes.unicode_minus']}")
    
    # 2. 检查 WenQuanYi 字体是否可用
    print("\n2. 检查 WenQuanYi 字体:")
    wenquanyi_fonts = [f for f in fontManager.ttflist if 'WenQuanYi' in f.name or '微米黑' in f.name]
    if wenquanyi_fonts:
        for font in wenquanyi_fonts[:3]:  # 只显示前3个
            print(f"   ✅ {font.name} - {font.fname}")
    else:
        print("   ❌ 未找到 WenQuanYi 字体")
    
    # 3. 检查字体缓存
    cache_dir = mpl.get_cachedir()
    cache_files = [f for f in os.listdir(cache_dir) if 'fontlist' in f]
    print(f"\n3. 字体缓存文件: {cache_files}")
    
    # 4. 测试字体渲染
    print("\n4. 测试字体渲染:")
    try:
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.text(0.5, 0.5, '中文测试', fontsize=20, ha='center', va='center')
        ax.set_title('字体测试')
        
        # 获取实际使用的字体
        text_obj = ax.texts[0]
        actual_font = text_obj.get_fontname()
        print(f"   实际使用字体: {actual_font}")
        
        plt.close(fig)  # 关闭图形避免显示
        
        if 'WenQuanYi' in actual_font or '微米黑' in actual_font:
            print("   ✅ 中文字体加载成功")
            return True
        else:
            print("   ❌ 中文字体加载失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 字体测试失败: {e}")
        return False

def apply_critical_fix():
    """应用关键修复步骤"""
    print("\n=== 应用关键修复 ===")
    
    # 关键步骤1: 强制重置 matplotlib
    print("1. 重置 matplotlib 配置...")
    mpl.rcdefaults()
    
    # 关键步骤2: 清除字体缓存
    print("2. 清除字体缓存...")
    cache_dir = mpl.get_cachedir()
    import glob
    cache_files = glob.glob(os.path.join(cache_dir, "*fontlist*"))
    for file in cache_files:
        try:
            os.remove(file)
            print(f"   删除: {os.path.basename(file)}")
        except:
            pass
    
    # 关键步骤3: 强制设置字体
    print("3. 强制设置中文字体...")
    plt.rcParams.update({
        'font.sans-serif': ['WenQuanYi Micro Hei', '文泉驿微米黑', 'Arial Unicode MS', 'SimHei'],
        'axes.unicode_minus': False,
        'font.size': 12
    })
    
    # 关键步骤4: 强制重新加载字体管理器
    print("4. 重新加载字体管理器...")
    try:
        # 强制重新扫描字体
        fontManager.__init__()
        print("   ✅ 字体管理器重新加载成功")
    except Exception as e:
        print(f"   ⚠️ 字体管理器重新加载失败: {e}")
    
    print("✅ 关键修复步骤完成")

def create_notebook_cell_code():
    """生成 Notebook 中使用的代码"""
    code = '''# === Jupyter Notebook 中文字体修复代码 ===
# 请将此代码复制到 Notebook 的第一个 cell 中运行

import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np
import os

# 强制重置和配置
mpl.rcdefaults()
plt.rcParams.update({
    'font.sans-serif': ['WenQuanYi Micro Hei', '文泉驿微米黑', 'Arial Unicode MS', 'SimHei'],
    'axes.unicode_minus': False,
    'font.size': 12
})

# 验证配置
print("✅ 字体配置完成")
print("当前字体:", plt.rcParams['font.sans-serif'][:2])

# 测试中文显示
def test_chinese():
    fig, ax = plt.subplots(figsize=(8, 5))
    x = np.linspace(0, 10, 100)
    ax.plot(x, np.sin(x), label='正弦波')
    ax.set_title('中文字体测试 - 低延迟趋势线分析')
    ax.set_xlabel('时间轴')
    ax.set_ylabel('价格变化')
    ax.legend()
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    # 检查字体
    title_font = ax.title.get_fontname()
    print(f"标题使用字体: {title_font}")
    if 'WenQuanYi' in title_font or '微米黑' in title_font:
        print("✅ 中文字体显示成功！")
    else:
        print("❌ 中文字体显示失败，请检查字体安装")

# 运行测试
test_chinese()'''
    
    return code

def main():
    """主函数"""
    print("Jupyter Notebook 中文字体快速修复工具")
    print("=" * 50)
    
    # 诊断问题
    font_ok = diagnose_font_issue()
    
    if not font_ok:
        # 应用修复
        apply_critical_fix()
        
        # 重新测试
        print("\n=== 重新测试 ===")
        font_ok = diagnose_font_issue()
    
    # 生成 Notebook 代码
    print("\n=== Notebook 使用代码 ===")
    notebook_code = create_notebook_cell_code()
    
    # 保存到文件
    with open('notebook_chinese_code.txt', 'w', encoding='utf-8') as f:
        f.write(notebook_code)
    
    print("已保存 Notebook 代码到: notebook_chinese_code.txt")
    
    print("\n" + "=" * 50)
    print("🎯 关键步骤总结:")
    print("1. ✅ 重置 matplotlib 配置")
    print("2. ✅ 清除字体缓存")
    print("3. ✅ 强制设置中文字体")
    print("4. ✅ 重新加载字体管理器")
    print()
    print("📋 在 Jupyter Notebook 中:")
    print("1. 重启内核 (Kernel -> Restart & Clear Output)")
    print("2. 复制 notebook_chinese_code.txt 中的代码到第一个 cell")
    print("3. 运行该 cell")
    print("4. 如果仍有问题，重启整个 Jupyter 服务")

if __name__ == "__main__":
    main()
