#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示连续信号过滤功能
对比过滤前后的效果
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from backtest_reporter import create_backtest_report

def create_demo_data():
    """创建演示数据"""
    dates = pd.date_range('2023-01-01', periods=30, freq='D')
    
    # 创建简单的价格数据
    np.random.seed(42)
    base_price = 100
    price_changes = np.random.randn(30) * 0.5
    prices = base_price + np.cumsum(price_changes)
    
    df = pd.DataFrame({
        'open': prices,
        'high': prices * 1.01,
        'low': prices * 0.99,
        'close': prices,
        'volume': np.random.randint(1000, 5000, 30)
    }, index=dates)
    
    return df

def demo_signal_strategy(df):
    """生成包含连续信号的演示策略"""
    signals = pd.Series(0, index=df.index)
    
    # 设置一些连续信号来演示过滤效果
    signals.iloc[3:7] = 1    # 连续4天买入信号
    signals.iloc[10] = -1    # 单独卖出信号
    signals.iloc[15:18] = -1 # 连续3天卖出信号
    signals.iloc[22:25] = 1  # 连续3天买入信号
    signals.iloc[28] = -1    # 单独卖出信号
    
    return signals

def compare_signal_filtering():
    """对比信号过滤前后的效果"""
    print("=== 连续信号过滤功能演示 ===")
    
    # 创建数据
    data = create_demo_data()
    signals = demo_signal_strategy(data)
    
    print("\n原始信号分布：")
    signal_days = []
    for i, signal in enumerate(signals):
        if signal != 0:
            signal_type = "买入" if signal == 1 else "卖出"
            date_str = signals.index[i].strftime('%m-%d')
            signal_days.append(f"{date_str}: {signal_type}")
    
    for day in signal_days:
        print(f"  {day}")
    
    # 手动计算过滤后的信号
    def filter_consecutive_signals(signal_series, target_value):
        filtered_indices = []
        prev_signal = 0
        
        for i, signal in enumerate(signal_series):
            if signal == target_value and prev_signal != target_value:
                filtered_indices.append(i)
            prev_signal = signal
        
        return filtered_indices
    
    buy_indices = filter_consecutive_signals(signals, 1)
    sell_indices = filter_consecutive_signals(signals, -1)
    
    print("\n过滤后显示的信号：")
    all_filtered = []
    for i in buy_indices:
        date_str = signals.index[i].strftime('%m-%d')
        all_filtered.append((i, f"{date_str}: 买入"))
    for i in sell_indices:
        date_str = signals.index[i].strftime('%m-%d')
        all_filtered.append((i, f"{date_str}: 卖出"))
    
    all_filtered.sort(key=lambda x: x[0])
    for _, day in all_filtered:
        print(f"  {day}")
    
    print(f"\n统计对比：")
    print(f"原始买入信号数: {(signals == 1).sum()} → 过滤后: {len(buy_indices)}")
    print(f"原始卖出信号数: {(signals == -1).sum()} → 过滤后: {len(sell_indices)}")
    
    # 生成回测报告
    print("\n生成回测报告（已应用信号过滤）...")
    fig = create_backtest_report(
        data,
        demo_signal_strategy,
        (),
        long_short=True,
        title="连续信号过滤演示"
    )
    
    print("\n✅ 功能说明：")
    print("1. 连续的买入信号（01-04到01-07）只显示第一个（01-04）")
    print("2. 单独的卖出信号（01-11）正常显示")
    print("3. 连续的卖出信号（01-16到01-18）只显示第一个（01-16）")
    print("4. 连续的买入信号（01-23到01-25）只显示第一个（01-23）")
    print("5. 单独的卖出信号（01-29）正常显示")
    print("\n这样可以让图表更清晰，避免重复标记造成的视觉混乱。")
    
    return fig

if __name__ == "__main__":
    fig = compare_signal_filtering()
    fig.show()